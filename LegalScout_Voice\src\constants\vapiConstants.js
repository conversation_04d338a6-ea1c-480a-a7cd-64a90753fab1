/**
 * Vapi integration constants
 */

// Default LegalScout assistant ID - system assistant for homepage
// This should be the LAST SELECTED <NAME_EMAIL>, not hardcoded
// NOTE: If you get a 400 error, this assistant ID may not exist in your Vapi account
// Run the debug script to check and create a new assistant if needed
// UPDATED: Using confirmed working assistant ID from damon attorney
export const DEFAULT_ASSISTANT_ID = 'eb8533fa-902e-46be-8ce9-df20f5c550d7'; // Confirmed working assistant ID

// Scout attorney configuration for home page calls
export const SCOUT_ATTORNEY_CONFIG = {
  id: "873b8f3a-2743-4c8f-861a-aac4503692a6",
  email: "<EMAIL>",
  firm_name: "LegalScout System",
  name: "Scout",
  welcome_message: "Hello! I'm <PERSON>, your AI legal assistant. How can I help you today?",
  vapi_instructions: "You are <PERSON>, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.",
  vapi_assistant_id: "eb8533fa-902e-46be-8ce9-df20f5c550d7", // System assistant ID (updated to confirmed working ID)
  voice_id: "echo", // OpenAI voice
  voice_provider: "openai",
  ai_model: "gpt-4o",
  primary_color: "#4B74AA",
  secondary_color: "#2C3E50",
  button_color: "#D85722"
};

// Vapi tool IDs
export const VAPI_TOOL_IDS = {
  LIVE_DOSSIER: "4a0d63cf-0b84-4eec-bddf-9c5869439d7e",
  CHECK_MATCHING_ATTORNEYS: "313b71f6-f4ea-44d4-a304-f5050e890693",
  LOCAL_ATTORNEY_SEARCH: "42375b0b-9c0b-481b-b445-d219b41f1988",
  GET_USER_INFO: "40e60896-518c-470a-a713-7abc2cd0c924"
};

// Make.com webhook URLs for tool integrations
export const MAKE_WEBHOOK_URLS = {
  LIVE_DOSSIER: "https://hook.us1.make.com/2c6ez4oq26ka5kv7h7zjhiy4v18l20t1",
  CHECK_MATCHING_ATTORNEYS: "https://hook.us1.make.com/4empo1t13htixhbkz7bs9ettvxs8g0j5",
  GET_USER_INFO: "https://hook.us1.make.com/2c6ez4oq26ka5kv7h7zjhiy4v18l20t1",
  LOCAL_ATTORNEY_SEARCH: "https://hook.us1.make.com/g6k2w1raan6ovyodlvfoevdtuuw09hb6"
};

// Call status constants
export const CALL_STATUS = {
  IDLE: 'idle',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  ERROR: 'error',
  COMPLETED: 'completed'
};

// Enhanced mock data for development
export const MOCK_DOSSIER_DATA = {
  name: "John Doe",
  location: {
    lat: 40.7128,
    lng: -74.0060,
    address: "New York, NY"
  },
  practiceArea: "Family Law",
  party: "Plaintiff",
  issue: "Child Custody and Support",
  urgency: "Medium",
  noteworthy: "Client has been denied visitation rights for 3 months"
};

// Practice area mapping for issue detection
export const PRACTICE_AREA_KEYWORDS = {
  // Family Law
  'divorce': 'Family Law',
  'custody': 'Family Law',
  'child support': 'Family Law',
  'alimony': 'Family Law',
  'adoption': 'Family Law',
  'prenuptial': 'Family Law',
  'postnuptial': 'Family Law',
  'separation': 'Family Law',

  // Estate Planning
  'will': 'Estate Planning',
  'trust': 'Estate Planning',
  'probate': 'Estate Planning',
  'inheritance': 'Estate Planning',
  'estate': 'Estate Planning',
  'power of attorney': 'Estate Planning',
  'living will': 'Estate Planning',
  'executor': 'Estate Planning',

  // Personal Injury
  'accident': 'Personal Injury',
  'injury': 'Personal Injury',
  'slip and fall': 'Personal Injury',
  'car accident': 'Personal Injury',
  'medical malpractice': 'Personal Injury',
  'wrongful death': 'Personal Injury',
  'product liability': 'Personal Injury',

  // Employment Law
  'discrimination': 'Employment Law',
  'wrongful termination': 'Employment Law',
  'harassment': 'Employment Law',
  'workplace': 'Employment Law',
  'severance': 'Employment Law',
  'wage dispute': 'Employment Law',
  'overtime': 'Employment Law',
  'hostile work': 'Employment Law',
  'fmla': 'Employment Law',

  // Business Law
  'contract': 'Business Law',
  'startup': 'Business Law',
  'incorporation': 'Business Law',
  'llc': 'Business Law',
  'partnership': 'Business Law',
  'corporate': 'Business Law',
  'shareholder': 'Business Law',
  'merger': 'Business Law',
  'acquisition': 'Business Law',

  // Real Estate Law
  'eviction': 'Real Estate Law',
  'foreclosure': 'Real Estate Law',
  'landlord': 'Real Estate Law',
  'tenant': 'Real Estate Law',
  'property': 'Real Estate Law',
  'lease': 'Real Estate Law',
  'zoning': 'Real Estate Law',
  'title': 'Real Estate Law',
  'closing': 'Real Estate Law',

  // Criminal Defense
  'dui': 'Criminal Defense',
  'arrest': 'Criminal Defense',
  'charged': 'Criminal Defense',
  'criminal': 'Criminal Defense',
  'felony': 'Criminal Defense',
  'misdemeanor': 'Criminal Defense',
  'probation': 'Criminal Defense',
  'parole': 'Criminal Defense',

  // Immigration Law
  'immigration': 'Immigration Law',
  'visa': 'Immigration Law',
  'green card': 'Immigration Law',
  'citizenship': 'Immigration Law',
  'deportation': 'Immigration Law',
  'asylum': 'Immigration Law',
  'naturalization': 'Immigration Law',
  'daca': 'Immigration Law',
  'foreign national': 'Immigration Law',

  // Bankruptcy Law
  'bankruptcy': 'Bankruptcy Law',
  'debt': 'Bankruptcy Law',
  'chapter 7': 'Bankruptcy Law',
  'chapter 11': 'Bankruptcy Law',
  'chapter 13': 'Bankruptcy Law',
  'foreclosure': 'Bankruptcy Law',
  'creditor': 'Bankruptcy Law',
  'repossession': 'Bankruptcy Law',

  // Intellectual Property
  'patent': 'Intellectual Property',
  'copyright': 'Intellectual Property',
  'trademark': 'Intellectual Property',
  'trade secret': 'Intellectual Property',
  'infringement': 'Intellectual Property',
  'licensing': 'Intellectual Property',
  'ip': 'Intellectual Property',
  'invention': 'Intellectual Property',
  'software': 'Intellectual Property'
};

// NYC borough coordinates
export const NYC_COORDINATES = {
  'manhattan': { lat: 40.7831, lng: -73.9712 },
  'brooklyn': { lat: 40.6782, lng: -73.9442 },
  'queens': { lat: 40.7282, lng: -73.7949 },
  'bronx': { lat: 40.8448, lng: -73.8648 },
  'staten island': { lat: 40.5795, lng: -74.1502 },
  'new york city': { lat: 40.7128, lng: -74.0060 },
  'nyc': { lat: 40.7128, lng: -74.0060 }
};