import { supabase } from '../lib/supabase';
import { getCurrentSubdomain } from '../utils/subdomainTester';

// Cache for subdomain configurations loaded from JSON
let subdomainConfigCache = null;

/**
 * Load subdomain configurations from the JSON file
 * @returns {Promise<Object>} Subdomain configurations
 */
const loadSubdomainConfigs = async () => {
  if (subdomainConfigCache) return subdomainConfigCache;

  try {
    const response = await fetch('/subdomain_config.json');
    if (!response.ok) throw new Error('Failed to load subdomain config');

    const data = await response.json();
    subdomainConfigCache = data;
    return data;
  } catch (error) {
    console.error('Error loading subdomain configs:', error);
    return {};
  }
};

export const getAttorneyConfig = (subdomain) => {
  // For now, use the local config directly since Supabase isn't fully implemented
  // In the future, this will be replaced with proper async handling

  // Normalized subdomain for consistency
  const normalizedSubdomain = subdomain?.toLowerCase().replace(/[^a-z0-9-]/g, '') || 'default';

  // Check if we're running on localhost
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  // LegalScout mascot logo - use local asset for development
  const legalScoutMascot = isLocalhost ? '/PRIMARY CLEAR.png' : 'https://legalscout.ai/static/media/logo.fe9193030f0d46d15d93.png';

  // Base configuration that applies to all subdomains
  const baseConfig = {
    firmName: "LegalScout",
    logo: legalScoutMascot,
    mascot: legalScoutMascot,
    vapiInstructions: "You are a general legal assistant. Collect basic information about the client's legal needs and help them find an appropriate attorney.",
    practiceAreas: ["Personal Injury", "Family Law", "Estate Planning"],
    practiceDescription: "**Welcome to LegalScout**\n\nOur AI-powered legal assistant is here to help you with your legal needs. Please customize this content in your dashboard to reflect your practice and expertise.\n\n*This is default content - please update your practice description in the dashboard to personalize your assistant.*",
    officeAddress: "123 Legal Avenue, Suite 500, New York, NY 10001",
    schedulingLink: "https://calendly.com/legalscout/consultation",
    primaryColor: "#4B74AA",
    secondaryColor: "#2C3E50",
    buttonColor: "#3498db",
    backgroundColor: "#1a1a1a",
    backgroundOpacity: 0.9,
    buttonText: "Start Consultation",
    buttonOpacity: 1,
    practiceAreaBackgroundOpacity: 0.1,
    textBackgroundColor: "#634C38",
    welcomeMessage: "Hello, I'm Scout from LegalScout. How can I help you today?",
    informationGathering: "To better assist you, I'll need a few details about your situation."
  };

  // Subdomain-specific configurations
  const subdomainConfigs = {
    "generalcounselonline": {
      firmName: "General Counsel Online",
      logo: "https://generalcounsel.online/wp-content/uploads/2021/12/GCO-Logo-RGB.png",
      mascot: "https://generalcounsel.online/wp-content/uploads/2022/01/GCO-Square-Logo.png",
      vapiInstructions: "I provide Business Law for clients in Pennsylvania. I want to know their company domicile, business structure, and legal or business issue they're facing. I need to ensure this is a business matter and not an individual issue. Refer the user to https://generalcounsel.online for more information."
    },
    "localhost": {
      firmName: "LegalScout Development",
      vapiInstructions: "This is a development environment for LegalScout. You are testing the voice interview functionality."
    }
  };

  // Get the config for the current subdomain or use default
  const subdomainConfig = subdomainConfigs[normalizedSubdomain] || {};

  // Merge the base config with the subdomain-specific config
  const mergedConfig = {
    ...baseConfig,
    ...subdomainConfig,
    isFallback: !subdomainConfigs[normalizedSubdomain]
  };

  return mergedConfig;
};

// Enhanced async version with Vapi assistant management
export const getAttorneyConfigAsync = async (subdomain) => {
  const logger = {
    info: (msg, data) => console.log(`[AttorneyConfig] ${msg}`, data || ''),
    warn: (msg, data) => console.warn(`[AttorneyConfig] ${msg}`, data || ''),
    error: (msg, data) => console.error(`[AttorneyConfig] ${msg}`, data || '')
  };

  // Log function entry for debugging
  logger.info('🚀 getAttorneyConfigAsync called', {
    subdomain,
    currentPath: window.location.pathname,
    hostname: window.location.hostname
  });

  try {
    // If no subdomain provided, get the current one
    const subdomainToUse = subdomain || getCurrentSubdomain();
    logger.info('🔍 Loading attorney config for subdomain:', subdomainToUse);

    // Normalized subdomain for consistency
    const normalizedSubdomain = subdomainToUse?.toLowerCase().replace(/[^a-z0-9-]/g, '') || 'default';
    logger.info('🔧 Normalized subdomain:', normalizedSubdomain);

    // Add hostname debugging
    logger.info('🌐 Current hostname:', window.location.hostname);
    logger.info('🌐 Current pathname:', window.location.pathname);

    // NEW: Try assistant-subdomain lookup first
    logger.info('🎯 Checking assistant-subdomain mapping for:', normalizedSubdomain);

    try {
      // Import the assistant subdomain service singleton
      const { assistantSubdomainService } = await import('../services/assistantSubdomainService');

      const assistantMapping = await assistantSubdomainService.getAssistantBySubdomain(normalizedSubdomain);

      if (assistantMapping) {
        logger.info('✅ Found assistant mapping:', {
          assistant_id: assistantMapping.assistant_id,
          attorney_id: assistantMapping.attorney_id,
          firm_name: assistantMapping.firm_name,
          is_primary: assistantMapping.is_primary
        });

        // Import supabase dynamically to ensure proper initialization
        const { getSupabaseClient } = await import('../lib/supabase');
        const supabaseClient = await getSupabaseClient();

        if (!supabaseClient || typeof supabaseClient.from !== 'function') {
          logger.error('❌ Supabase client not available for attorney lookup');
          throw new Error('Supabase client not available');
        }

        // Get attorney data using the attorney_id from the mapping
        const { data: attorney, error: attorneyError } = await supabaseClient
          .from('attorneys')
          .select('*')
          .eq('id', assistantMapping.attorney_id)
          .single();

        if (!attorneyError && attorney) {
          logger.info('✅ Loaded attorney via assistant mapping:', attorney.firm_name);

          // CRITICAL: Load assistant-specific UI config
          let assistantUIConfig = null;
          try {
            const { data: uiConfig, error: uiError } = await supabaseClient
              .from('assistant_ui_configs')
              .select('*')
              .eq('assistant_id', assistantMapping.assistant_id)
              .single();

            if (!uiError && uiConfig) {
              assistantUIConfig = uiConfig;
              logger.info('✅ Loaded assistant-specific UI config:', {
                assistant_id: uiConfig.assistant_id,
                firm_name: uiConfig.firm_name,
                voice_id: uiConfig.voice_id,
                primary_color: uiConfig.primary_color
              });
            } else {
              logger.info('📭 No assistant-specific UI config found, using attorney defaults');
            }
          } catch (uiConfigError) {
            logger.warn('⚠️ Failed to load assistant UI config:', uiConfigError.message);
          }

          // Create comprehensive attorney config with assistant-specific UI variables
          const attorneyConfig = {
            ...getAttorneyConfig('default'), // Start with default config
            ...attorney, // Merge attorney data
            ...(assistantUIConfig && {
              // Override with assistant-specific UI config if available
              firmName: assistantUIConfig.firm_name || attorney.firm_name || attorney.name || "LegalScout",
              primaryColor: assistantUIConfig.primary_color || attorney.primary_color,
              secondaryColor: assistantUIConfig.secondary_color || attorney.secondary_color,
              buttonColor: assistantUIConfig.button_color || attorney.button_color,
              backgroundColor: assistantUIConfig.background_color || attorney.background_color,
              backgroundOpacity: assistantUIConfig.background_opacity || attorney.background_opacity,
              buttonOpacity: assistantUIConfig.button_opacity || attorney.button_opacity,
              practiceAreaBackgroundOpacity: assistantUIConfig.practice_area_background_opacity || attorney.practice_area_background_opacity,
              textBackgroundColor: assistantUIConfig.text_background_color || attorney.text_background_color,
              welcomeMessage: assistantUIConfig.welcome_message || attorney.welcome_message,
              practiceDescription: assistantUIConfig.practice_description || attorney.practice_description,
              informationGathering: assistantUIConfig.information_gathering || attorney.information_gathering,
              vapiInstructions: assistantUIConfig.vapi_instructions || attorney.vapi_instructions,
              vapiContext: assistantUIConfig.vapi_context || attorney.vapi_context,
              voiceId: assistantUIConfig.voice_id || attorney.voice_id,
              voiceProvider: assistantUIConfig.voice_provider || attorney.voice_provider,
              aiModel: assistantUIConfig.ai_model || attorney.ai_model,
              logo: assistantUIConfig.logo_url || attorney.logo_url,
              mascot: assistantUIConfig.mascot_url || attorney.profile_image,
              buttonImage: assistantUIConfig.assistant_image_url || attorney.button_image || attorney.profile_image,
              practiceAreas: assistantUIConfig.practice_areas || attorney.practice_areas,
              officeAddress: assistantUIConfig.office_address || attorney.office_address,
              schedulingLink: assistantUIConfig.scheduling_link || attorney.scheduling_link
            }),

            // Assistant mapping specific fields
            current_assistant_id: assistantMapping.assistant_id,
            assistant_subdomain: assistantMapping.subdomain,
            is_primary_assistant: assistantMapping.is_primary,

            // Ensure proper field mapping (fallback if no UI config)
            firmName: assistantUIConfig?.firm_name || attorney.firm_name || attorney.name || "LegalScout",
            vapi_assistant_id: assistantMapping.assistant_id, // Use the specific assistant for this subdomain

            // Status tracking
            vapiSyncStatus: 'assistant_subdomain_mapped',
            loadedVia: 'assistant_subdomain_mapping',
            hasAssistantUIConfig: !!assistantUIConfig
          };

          logger.info('🎯 Returning attorney config via assistant mapping:', {
            firmName: attorneyConfig.firmName,
            vapi_assistant_id: attorneyConfig.vapi_assistant_id,
            subdomain: attorneyConfig.assistant_subdomain
          });

          return attorneyConfig;
        } else {
          logger.warn('⚠️ Attorney lookup failed for mapping:', {
            attorney_id: assistantMapping.attorney_id,
            error: attorneyError?.message
          });
        }
      } else {
        logger.info('📭 No assistant mapping found for subdomain:', normalizedSubdomain);
      }
    } catch (assistantLookupError) {
      logger.warn('⚠️ Assistant-subdomain lookup failed, falling back to attorney lookup:', assistantLookupError.message);
    }

    // FALLBACK: Try traditional attorney subdomain lookup
    logger.info('📊 Querying Supabase with traditional attorney lookup:', {
      table: 'attorneys',
      field: 'subdomain',
      value: normalizedSubdomain,
      originalSubdomain: subdomainToUse
    });

    // Check if Supabase is available
    if (!supabase) {
      logger.error('❌ Supabase client is not available');
      throw new Error('Supabase client is not available');
    }

    // Use the same method as subdomain availability checker
    // Query using array method instead of .single() to avoid errors
    const { data: queryResults, error } = await supabase
      .from('attorneys')
      .select('*') // Select all fields to get all available data
      .eq('subdomain', normalizedSubdomain);

    logger.info('📊 Subdomain query results:', {
      queryResults,
      error: error?.message,
      resultCount: queryResults?.length || 0,
      hasSupabase: !!supabase,
      supabaseUrl: supabase?.supabaseUrl
    });

    // Extract the first result if found
    let data = null;
    if (!error && queryResults && queryResults.length > 0) {
      data = queryResults[0];
      logger.info('✅ Found attorney record:', {
        id: data.id,
        subdomain: data.subdomain,
        firmName: data.firm_name,
        email: data.email
      });
    } else if (normalizedSubdomain === 'damon') {
      // Fallback: Try by current authenticated user's email
      logger.info('🔄 Subdomain lookup failed for damon, trying authenticated user email fallback');

      // Get current authenticated user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user?.email) {
        logger.error('No authenticated user found for damon subdomain fallback');
        return { data: null, error: { message: 'No authenticated user found' } };
      }

      const { data: emailResults, error: emailError } = await supabase
        .from('attorneys')
        .select('*')
        .eq('email', user.email);

      if (!emailError && emailResults && emailResults.length > 0) {
        data = emailResults[0];
        logger.info('✅ Found attorney by email fallback:', {
          id: data.id,
          subdomain: data.subdomain,
          email: data.email
        });
      } else {
        // Debug: List all attorneys to see what's in the database
        logger.info('🔍 Email lookup also failed, checking what attorneys exist in database');
        const { data: allAttorneys, error: allError } = await supabase
          .from('attorneys')
          .select('id, subdomain, email, firm_name')
          .limit(10);

        if (!allError && allAttorneys) {
          logger.info('📋 Available attorneys in database:', allAttorneys);
        } else {
          logger.error('❌ Failed to query attorneys table:', allError?.message);
        }
      }
    } else {
      logger.warn('⚠️ No attorney found for subdomain:', normalizedSubdomain);
    }

    logger.info('Supabase query result:', {
      found: !!data,
      error: error?.message,
      firmName: data?.firm_name,
      assistantId: data?.vapi_assistant_id,
      attorneyId: data?.id,
      subdomain: data?.subdomain,
      querySubdomain: normalizedSubdomain
    });

    if (!error && data) {
      logger.info('Found attorney in database:', {
        id: data.id,
        firmName: data.firm_name,
        subdomain: data.subdomain,
        email: data.email
      });
      // Create a comprehensive config object with all fields from Supabase
      const attorneyConfig = {
        ...getAttorneyConfig('default'), // Start with default config
        id: data.id,
        firmName: data.firm_name || data.name || "LegalScout",
        logo: data.logo_url || getAttorneyConfig('default').logo,
        mascot: data.profile_image || getAttorneyConfig('default').mascot,
        buttonImage: data.button_image || data.profile_image || getAttorneyConfig('default').mascot,
        subdomain: data.subdomain || normalizedSubdomain,

        // Vapi configuration fields - these are the most important for our single source of truth
        vapi_assistant_id: data.vapi_assistant_id || null, // Primary key for Vapi integration
        vapiInstructions: data.vapi_instructions || getAttorneyConfig('default').vapiInstructions,
        vapiContext: data.vapi_context || "",
        welcomeMessage: data.welcome_message || getAttorneyConfig('default').welcomeMessage,
        informationGathering: data.information_gathering || getAttorneyConfig('default').informationGathering,
        voiceId: data.voice_id || null,
        voiceProvider: data.voice_provider || "11labs",
        aiModel: data.ai_model || "gpt-4o",

        // Analysis configuration fields
        summaryPrompt: data.summary_prompt || null,
        structuredDataPrompt: data.structured_data_prompt || null,
        structuredDataSchema: data.structured_data_schema || null,
        successEvaluationPrompt: data.success_evaluation_prompt || null,

        // UI configuration fields
        interactionDepositUrl: data.interaction_deposit_url || "",
        practiceAreas: data.practice_areas || [],
        titleText: data.title_text || data.firm_name || "LegalScout",
        primaryColor: data.primary_color || getAttorneyConfig('default').primaryColor,
        secondaryColor: data.secondary_color || getAttorneyConfig('default').secondaryColor,
        buttonColor: data.button_color || getAttorneyConfig('default').buttonColor,
        backgroundColor: data.background_color || getAttorneyConfig('default').backgroundColor,
        backgroundOpacity: data.background_opacity || getAttorneyConfig('default').backgroundOpacity,
        buttonText: data.button_text || getAttorneyConfig('default').buttonText,
        buttonOpacity: data.button_opacity || getAttorneyConfig('default').buttonOpacity,
        practiceAreaBackgroundOpacity: data.practice_area_background_opacity || getAttorneyConfig('default').practiceAreaBackgroundOpacity,
        textBackgroundColor: data.text_background_color || getAttorneyConfig('default').textBackgroundColor,
        officeAddress: data.office_address || "",
        schedulingLink: data.scheduling_link || "",
        practiceDescription: data.practice_description || getAttorneyConfig('default').practiceDescription,
        theme: data.theme || "dark",

        // Custom fields
        customFields: data.custom_fields || null,

        // Metadata
        isFallback: false,
        lastUpdated: data.updated_at || new Date().toISOString()
      };

      // Log important fields for debugging
      logger.info('Attorney config loaded from DB:', {
        id: attorneyConfig.id,
        subdomain: attorneyConfig.subdomain,
        vapi_assistant_id: attorneyConfig.vapi_assistant_id,
        hasVapiInstructions: !!attorneyConfig.vapiInstructions,
        lastUpdated: attorneyConfig.lastUpdated
      });

      // Ensure Vapi assistant exists and is synchronized (only in appropriate contexts)
      try {
        // Only run Vapi sync if we're in a dashboard context, not during auth flow or homepage
        const currentPath = window.location.pathname;
        const isAuthFlow = currentPath.includes('/auth/callback') || currentPath.includes('/complete-profile');
        const isSubdomainPage = currentPath === '/' && window.location.hostname !== 'localhost';
        const isHomepage = (currentPath === '/' || currentPath === '/home') && window.location.hostname === 'localhost';
        const isDashboard = currentPath.includes('/dashboard') || window.location.hostname.includes('dashboard');

        // Log context detection for debugging
        logger.info('🔍 Context detection:', {
          currentPath,
          hostname: window.location.hostname,
          isAuthFlow,
          isSubdomainPage,
          isHomepage,
          isDashboard
        });

        // Skip Vapi sync for homepage and subdomain pages to avoid CORS issues and blocking
        if (isHomepage || isSubdomainPage) {
          const context = isHomepage ? 'homepage' : 'subdomain page';
          logger.info(`🌐 ${context} detected - returning config without Vapi sync to avoid CORS issues`);
          return attorneyConfig;
        } else if (isAuthFlow) {
          logger.info('🚫 Skipping Vapi sync during auth flow');
          return attorneyConfig;
        } else if (isDashboard) {
          logger.info('🔄 Running Vapi assistant sync (dashboard context)');
          const enhancedConfig = await ensureVapiAssistantSync(attorneyConfig);
          return enhancedConfig;
        } else {
          logger.info('🔄 Running Vapi assistant sync (default context)');
          const enhancedConfig = await ensureVapiAssistantSync(attorneyConfig);
          return enhancedConfig;
        }
      } catch (assistantError) {
        logger.error('❌ Vapi assistant sync failed, returning config without sync:', {
          error: assistantError.message,
          stack: assistantError.stack
        });
        return attorneyConfig;
      }
    } else {
      // If not found in Supabase, try to load from subdomain_config.json
      logger.info('Attorney not found in database, trying JSON config');
      const subdomainConfigs = await loadSubdomainConfigs();
      if (subdomainConfigs && subdomainConfigs[normalizedSubdomain]) {
        logger.info('Found attorney in JSON config');
        const config = subdomainConfigs[normalizedSubdomain];
        return {
          firmName: config.firmName || "LegalScout",
          logo: config.logo || getAttorneyConfig('default').logo,
          mascot: config.mascot || getAttorneyConfig('default').mascot,
          vapiInstructions: config.vapiInstructions || getAttorneyConfig('default').vapiInstructions,
          vapiContext: config.vapiContext || "",
          interactionDepositUrl: config.interactionDepositUrl || config.interaction_deposit_url || "",
          practiceAreas: config.practiceAreas || [],
          isFallback: false
        };
      }
    }
  } catch (e) {
    logger.error('❌ Error loading attorney config:', {
      message: e.message,
      stack: e.stack,
      subdomain: subdomain,
      hostname: window.location.hostname
    });
  }

  // Fall back to local config if all else fails
  logger.info('🔄 Falling back to local config for subdomain:', subdomain);
  const fallbackConfig = getAttorneyConfig(subdomain);
  logger.info('📦 Fallback config:', {
    firmName: fallbackConfig.firmName,
    isFallback: fallbackConfig.isFallback
  });

  // IMPORTANT: Also check for homepage in fallback path to prevent assistant creation
  const currentPath = window.location.pathname;
  const hostname = window.location.hostname;
  const isHomepage = (currentPath === '/' || currentPath === '/home') &&
                    (hostname === 'localhost' || hostname.includes('legalscout.net') || hostname.includes('vercel.app'));

  if (isHomepage) {
    logger.info('🌐 Homepage detected in fallback path - using system assistant');
    return {
      ...fallbackConfig,
      vapi_assistant_id: 'eb8533fa-902e-46be-8ce9-df20f5c550d7', // Use confirmed working system assistant
      vapiSyncStatus: 'homepage_system_assistant'
    };
  }

  return fallbackConfig;
};

/**
 * Ensure Vapi assistant exists and is synchronized with attorney data
 * @param {Object} attorneyConfig - The attorney configuration object
 * @returns {Promise<Object>} - Enhanced attorney config with assistant status
 */
const ensureVapiAssistantSync = async (attorneyConfig) => {
  const logger = {
    info: (msg, data) => console.log(`[VapiSync] ${msg}`, data || ''),
    warn: (msg, data) => console.warn(`[VapiSync] ${msg}`, data || ''),
    error: (msg, data) => console.error(`[VapiSync] ${msg}`, data || '')
  };

  // CRITICAL: Check for homepage to prevent assistant creation
  const currentPath = window.location.pathname;
  const hostname = window.location.hostname;
  const isHomepage = (currentPath === '/' || currentPath === '/home') &&
                    (hostname === 'localhost' || hostname.includes('legalscout.net') || hostname.includes('vercel.app'));

  if (isHomepage) {
    logger.info('🚫 Homepage detected in ensureVapiAssistantSync - using system assistant');
    return {
      ...attorneyConfig,
      vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', // Use system <NAME_EMAIL>
      vapiSyncStatus: 'homepage_system_assistant'
    };
  }

  logger.info('🔄 ensureVapiAssistantSync called for:', attorneyConfig.firmName);

  try {
    // Import the enhanced Vapi MCP service
    const { enhancedVapiMcpService } = await import('../services/EnhancedVapiMcpService');

    // Connect to Vapi MCP service
    const apiKey = import.meta.env.VITE_VAPI_SECRET_KEY || import.meta.env.VITE_VAPI_PUBLIC_KEY;
    if (!apiKey) {
      logger.warn('No Vapi API key found, skipping assistant sync');
      return { ...attorneyConfig, vapiSyncStatus: 'no_api_key' };
    }

    await enhancedVapiMcpService.connect(apiKey);
    logger.info('Connected to Vapi MCP service');

    // Check if assistant exists
    if (attorneyConfig.vapi_assistant_id) {
      logger.info('Checking existing assistant:', attorneyConfig.vapi_assistant_id);

      try {
        const existingAssistant = await enhancedVapiMcpService.getAssistant(attorneyConfig.vapi_assistant_id);

        if (existingAssistant) {
          logger.info('Assistant verified in Vapi:', {
            id: existingAssistant.id,
            name: existingAssistant.name
          });

          return {
            ...attorneyConfig,
            vapiSyncStatus: 'verified',
            vapiAssistantName: existingAssistant.name
          };
        }
      } catch (error) {
        logger.warn('Assistant not found in Vapi, will create new one:', error.message);
      }
    }

    // Create new assistant if none exists or verification failed
    logger.info('Creating new Vapi assistant for:', attorneyConfig.firmName);

    // CRITICAL: Prevent assistant creation on homepage
    const currentPath = window.location.pathname;
    const hostname = window.location.hostname;
    const isHomepage = (currentPath === '/' || currentPath === '/home') &&
                      (hostname === 'localhost' || hostname.includes('legalscout.net') || hostname.includes('vercel.app'));

    if (isHomepage) {
      logger.info('🚫 Homepage detected in attorneys.js - using system assistant');
      return {
        ...attorneyConfig,
        vapi_assistant_id: 'eb8533fa-902e-46be-8ce9-df20f5c550d7', // Use confirmed working system assistant
        vapiSyncStatus: 'homepage_system_assistant',
        vapiAssistantName: 'LegalScout System'
      };
    }

    const assistantConfig = {
      name: attorneyConfig.firmName || 'LegalScout Assistant',
      firstMessage: attorneyConfig.welcomeMessage || 'Hello, how can I help you today?',
      firstMessageMode: "assistant-speaks-first",
      model: {
        provider: "openai",
        model: attorneyConfig.aiModel || "gpt-4o",
        messages: [
          {
            role: "system",
            content: attorneyConfig.vapiInstructions || "You are a helpful legal assistant."
          }
        ]
      },
      voice: {
        provider: "openai", // Always use openai provider for alloy voice
        voiceId: "alloy" // OpenAI voice - reliable and commonly used
      },
      transcriber: {
        provider: "deepgram",
        model: "nova-3"
      }
    };

    const newAssistant = await enhancedVapiMcpService.createAssistant(assistantConfig);

    if (newAssistant && newAssistant.id && !newAssistant.id.startsWith('mock-')) {
      logger.info('Created new Vapi assistant:', {
        id: newAssistant.id,
        name: newAssistant.name
      });

      // Update Supabase with the new assistant ID (only if we have a valid attorney ID)
      if (attorneyConfig.id) {
        const { error: updateError } = await supabase
          .from('attorneys')
          .update({ vapi_assistant_id: newAssistant.id })
          .eq('id', attorneyConfig.id);

        if (updateError) {
          logger.error('Failed to update assistant ID in Supabase:', updateError.message);
        } else {
          logger.info('Updated assistant ID in Supabase');
        }
      } else {
        logger.warn('Cannot update Supabase: attorney ID is undefined', {
          firmName: attorneyConfig.firmName,
          subdomain: attorneyConfig.subdomain,
          assistantId: newAssistant.id
        });
      }

      return {
        ...attorneyConfig,
        vapi_assistant_id: newAssistant.id,
        vapiSyncStatus: 'created',
        vapiAssistantName: newAssistant.name
      };
    } else {
      logger.warn('Mock assistant created, Vapi service may be unavailable');
      return {
        ...attorneyConfig,
        vapiSyncStatus: 'mock_created',
        vapiAssistantName: newAssistant?.name || 'Mock Assistant'
      };
    }

  } catch (error) {
    logger.error('Vapi assistant sync failed:', error.message);
    return {
      ...attorneyConfig,
      vapiSyncStatus: 'sync_failed',
      vapiSyncError: error.message
    };
  }
};