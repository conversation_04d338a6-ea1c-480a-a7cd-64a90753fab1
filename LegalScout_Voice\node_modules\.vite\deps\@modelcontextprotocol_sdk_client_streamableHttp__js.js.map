{"version": 3, "sources": ["../../eventsource-parser/src/stream.ts", "../../@modelcontextprotocol/sdk/src/client/streamableHttp.ts"], "sourcesContent": ["import {createParser} from './parse.ts'\nimport type {EventSourceMessage, EventSourceParser} from './types.ts'\n\n/**\n * Options for the EventSourceParserStream.\n *\n * @public\n */\nexport interface StreamOptions {\n  /**\n   * Behavior when a parsing error occurs.\n   *\n   * - A custom function can be provided to handle the error.\n   * - `'terminate'` will error the stream and stop parsing.\n   * - Any other value will ignore the error and continue parsing.\n   *\n   * @defaultValue `undefined`\n   */\n  onError?: 'terminate' | ((error: Error) => void)\n\n  /**\n   * Callback for when a reconnection interval is sent from the server.\n   *\n   * @param retry - The number of milliseconds to wait before reconnecting.\n   */\n  onRetry?: (retry: number) => void\n\n  /**\n   * Callback for when a comment is encountered in the stream.\n   *\n   * @param comment - The comment encountered in the stream.\n   */\n  onComment?: (comment: string) => void\n}\n\n/**\n * A TransformStream that ingests a stream of strings and produces a stream of `EventSourceMessage`.\n *\n * @example Basic usage\n * ```\n * const eventStream =\n *   response.body\n *     .pipeThrough(new TextDecoderStream())\n *     .pipeThrough(new EventSourceParserStream())\n * ```\n *\n * @example Terminate stream on parsing errors\n * ```\n * const eventStream =\n *  response.body\n *   .pipeThrough(new TextDecoderStream())\n *   .pipeThrough(new EventSourceParserStream({terminateOnError: true}))\n * ```\n *\n * @public\n */\nexport class EventSourceParserStream extends TransformStream<string, EventSourceMessage> {\n  constructor({onError, onRetry, onComment}: StreamOptions = {}) {\n    let parser!: EventSourceParser\n\n    super({\n      start(controller) {\n        parser = createParser({\n          onEvent: (event) => {\n            controller.enqueue(event)\n          },\n          onError(error) {\n            if (onError === 'terminate') {\n              controller.error(error)\n            } else if (typeof onError === 'function') {\n              onError(error)\n            }\n\n            // Ignore by default\n          },\n          onRetry,\n          onComment,\n        })\n      },\n      transform(chunk) {\n        parser.feed(chunk)\n      },\n    })\n  }\n}\n\nexport {type ErrorType, ParseError} from './errors.ts'\nexport type {EventSourceMessage} from './types.ts'\n", null], "mappings": ";;;;;;;;;;;;;;;AAwDO,IAAM,0BAAN,cAAsC,gBAA4C;EACvF,YAAY,EAAC,SAAS,SAAS,UAAS,IAAmB,CAAA,GAAI;AACzD,QAAA;AAEE,UAAA;MACJ,MAAM,YAAY;AAChB,iBAAS,aAAa;UACpB,SAAS,CAAC,UAAU;AAClB,uBAAW,QAAQ,KAAK;UAC1B;UACA,QAAQ,OAAO;AACT,wBAAY,cACd,WAAW,MAAM,KAAK,IACb,OAAO,WAAY,cAC5B,QAAQ,KAAK;UAIjB;UACA;UACA;QAAA,CACD;MACH;MACA,UAAU,OAAO;AACf,eAAO,KAAK,KAAK;MAAA;IACnB,CACD;EAAA;AAEL;;;AC9EA,IAAM,+CAAkF;EACtF,0BAA0B;EAC1B,sBAAsB;EACtB,6BAA6B;EAC7B,YAAY;;AAGR,IAAO,sBAAP,cAAmC,MAAK;EAC5C,YACkB,MAChB,SAA2B;AAE3B,UAAM,0BAA0B,OAAO,EAAE;AAHzB,SAAA,OAAA;EAIlB;;AAmGI,IAAO,gCAAP,MAAoC;EAaxC,YACE,KACA,MAA2C;;AAE3C,SAAK,OAAO;AACZ,SAAK,uBAAuB;AAC5B,SAAK,eAAe,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM;AAC1B,SAAK,gBAAgB,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM;AAC3B,SAAK,aAAa,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM;AACxB,SAAK,wBAAuB,KAAA,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,yBAAmB,QAAA,OAAA,SAAA,KAAI;EAC3D;EAEQ,MAAM,iBAAc;;AAC1B,QAAI,CAAC,KAAK,eAAe;AACvB,YAAM,IAAI,kBAAkB,kBAAkB;IAChD;AAEA,QAAI;AACJ,QAAI;AACF,eAAS,MAAM,KAAK,KAAK,eAAe,EAAE,WAAW,KAAK,MAAM,qBAAqB,KAAK,qBAAoB,CAAE;IAClH,SAAS,OAAO;AACd,OAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,KAAc;AAC7B,YAAM;IACR;AAEA,QAAI,WAAW,cAAc;AAC3B,YAAM,IAAI,kBAAiB;IAC7B;AAEA,WAAO,MAAM,KAAK,gBAAgB,EAAE,iBAAiB,OAAS,CAAE;EAClE;EAEQ,MAAM,iBAAc;;AAC1B,UAAM,UAAuB,CAAA;AAC7B,QAAI,KAAK,eAAe;AACtB,YAAM,SAAS,MAAM,KAAK,cAAc,OAAM;AAC9C,UAAI,QAAQ;AACV,gBAAQ,eAAe,IAAI,UAAU,OAAO,YAAY;MAC1D;IACF;AAEA,QAAI,KAAK,YAAY;AACnB,cAAQ,gBAAgB,IAAI,KAAK;IACnC;AAEA,WAAO,IAAI,QACT,EAAE,GAAG,SAAS,IAAG,KAAA,KAAK,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,QAAO,CAAE;EAEjD;EAGQ,MAAM,gBAAgB,SAAwB;;AACpD,UAAM,EAAE,gBAAe,IAAK;AAC5B,QAAI;AAGF,YAAM,UAAU,MAAM,KAAK,eAAc;AACzC,cAAQ,IAAI,UAAU,mBAAmB;AAGzC,UAAI,iBAAiB;AACnB,gBAAQ,IAAI,iBAAiB,eAAe;MAC9C;AAEA,YAAM,WAAW,MAAM,MAAM,KAAK,MAAM;QACtC,QAAQ;QACR;QACA,SAAQ,KAAA,KAAK,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE;OAChC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,YAAI,SAAS,WAAW,OAAO,KAAK,eAAe;AAEjD,iBAAO,MAAM,KAAK,eAAc;QAClC;AAIA,YAAI,SAAS,WAAW,KAAK;AAC3B;QACF;AAEA,cAAM,IAAI,oBACR,SAAS,QACT,8BAA8B,SAAS,UAAU,EAAE;MAEvD;AAEA,WAAK,iBAAiB,SAAS,MAAM,OAAO;IAC9C,SAAS,OAAO;AACd,OAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,KAAc;AAC7B,YAAM;IACR;EACF;;;;;;;EASQ,0BAA0B,SAAe;AAE/C,UAAM,eAAe,KAAK,qBAAqB;AAC/C,UAAM,aAAa,KAAK,qBAAqB;AAC7C,UAAM,WAAW,KAAK,qBAAqB;AAG3C,WAAO,KAAK,IAAI,eAAe,KAAK,IAAI,YAAY,OAAO,GAAG,QAAQ;EAExE;;;;;;;EAQQ,sBAAsB,SAA0B,eAAe,GAAC;;AAEtE,UAAM,aAAa,KAAK,qBAAqB;AAG7C,QAAI,aAAa,KAAK,gBAAgB,YAAY;AAChD,OAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,IAAI,MAAM,kCAAkC,UAAU,aAAa,CAAC;AACnF;IACF;AAGA,UAAM,QAAQ,KAAK,0BAA0B,YAAY;AAGzD,eAAW,MAAK;AAEd,WAAK,gBAAgB,OAAO,EAAE,MAAM,WAAQ;;AAC1C,SAAAA,MAAA,KAAK,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,MAAG,IAAI,MAAM,mCAAmC,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK,CAAC,EAAE,CAAC;AAErH,aAAK,sBAAsB,SAAS,eAAe,CAAC;MACtD,CAAC;IACH,GAAG,KAAK;EACV;EAEQ,iBAAiB,QAA2C,SAAwB;AAC1F,QAAI,CAAC,QAAQ;AACX;IACF;AACA,UAAM,EAAE,mBAAmB,gBAAe,IAAK;AAE/C,QAAI;AACJ,UAAM,gBAAgB,YAAW;;AAG/B,UAAI;AAEF,cAAM,SAAS,OACZ,YAAY,IAAI,kBAAiB,CAAE,EACnC,YAAY,IAAI,wBAAuB,CAAE,EACzC,UAAS;AAGZ,eAAO,MAAM;AACX,gBAAM,EAAE,OAAO,OAAO,KAAI,IAAK,MAAM,OAAO,KAAI;AAChD,cAAI,MAAM;AACR;UACF;AAGA,cAAI,MAAM,IAAI;AACZ,0BAAc,MAAM;AACpB,kCAAiB,QAAjB,sBAAiB,SAAA,SAAjB,kBAAoB,MAAM,EAAE;UAC9B;AAEA,cAAI,CAAC,MAAM,SAAS,MAAM,UAAU,WAAW;AAC7C,gBAAI;AACF,oBAAM,UAAU,qBAAqB,MAAM,KAAK,MAAM,MAAM,IAAI,CAAC;AACjE,kBAAI,oBAAoB,UAAa,kBAAkB,OAAO,GAAG;AAC/D,wBAAQ,KAAK;cACf;AACA,eAAA,KAAA,KAAK,eAAS,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,OAAO;YAC1B,SAAS,OAAO;AACd,eAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,KAAc;YAC/B;UACF;QACF;MACF,SAAS,OAAO;AAEd,SAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,IAAI,MAAM,4BAA4B,KAAK,EAAE,CAAC;AAG7D,YAAI,KAAK,oBAAoB,CAAC,KAAK,iBAAiB,OAAO,SAAS;AAElE,cAAI,gBAAgB,QAAW;AAC7B,gBAAI;AACF,mBAAK,sBAAsB;gBACzB,iBAAiB;gBACjB;gBACA;iBACC,CAAC;YACN,SACOC,QAAO;AACZ,eAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,IAAI,MAAM,wBAAwBA,kBAAiB,QAAQA,OAAM,UAAU,OAAOA,MAAK,CAAC,EAAE,CAAC;YAE5G;UACF;QACF;MACF;IACF;AACA,kBAAa;EACf;EAEA,MAAM,QAAK;AACT,QAAI,KAAK,kBAAkB;AACzB,YAAM,IAAI,MACR,wHAAwH;IAE5H;AAEA,SAAK,mBAAmB,IAAI,gBAAe;EAC7C;;;;EAKA,MAAM,WAAW,mBAAyB;AACxC,QAAI,CAAC,KAAK,eAAe;AACvB,YAAM,IAAI,kBAAkB,kBAAkB;IAChD;AAEA,UAAM,SAAS,MAAM,KAAK,KAAK,eAAe,EAAE,WAAW,KAAK,MAAM,mBAAmB,qBAAqB,KAAK,qBAAoB,CAAE;AACzI,QAAI,WAAW,cAAc;AAC3B,YAAM,IAAI,kBAAkB,qBAAqB;IACnD;EACF;EAEA,MAAM,QAAK;;AAET,KAAA,KAAA,KAAK,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,MAAK;AAE5B,KAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAA;EACd;EAEA,MAAM,KAAK,SAA4C,SAAmF;;AACxI,QAAI;AACF,YAAM,EAAE,iBAAiB,kBAAiB,IAAK,WAAW,CAAA;AAE1D,UAAI,iBAAiB;AAEnB,aAAK,gBAAgB,EAAE,iBAAiB,iBAAiB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,OAAS,CAAE,EAAE,MAAM,SAAM;AAAA,cAAAD;AAAC,kBAAAA,MAAA,KAAK,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,MAAG,GAAG;QAAC,CAAA;AAC/I;MACF;AAEA,YAAM,UAAU,MAAM,KAAK,eAAc;AACzC,cAAQ,IAAI,gBAAgB,kBAAkB;AAC9C,cAAQ,IAAI,UAAU,qCAAqC;AAE3D,YAAM,OAAO;QACX,GAAG,KAAK;QACR,QAAQ;QACR;QACA,MAAM,KAAK,UAAU,OAAO;QAC5B,SAAQ,KAAA,KAAK,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE;;AAGjC,YAAM,WAAW,MAAM,MAAM,KAAK,MAAM,IAAI;AAG5C,YAAM,YAAY,SAAS,QAAQ,IAAI,gBAAgB;AACvD,UAAI,WAAW;AACb,aAAK,aAAa;MACpB;AAEA,UAAI,CAAC,SAAS,IAAI;AAChB,YAAI,SAAS,WAAW,OAAO,KAAK,eAAe;AAEjD,eAAK,uBAAuB,2BAA2B,QAAQ;AAE/D,gBAAM,SAAS,MAAM,KAAK,KAAK,eAAe,EAAE,WAAW,KAAK,MAAM,qBAAqB,KAAK,qBAAoB,CAAE;AACtH,cAAI,WAAW,cAAc;AAC3B,kBAAM,IAAI,kBAAiB;UAC7B;AAGA,iBAAO,KAAK,KAAK,OAAO;QAC1B;AAEA,cAAM,OAAO,MAAM,SAAS,KAAI,EAAG,MAAM,MAAM,IAAI;AACnD,cAAM,IAAI,MACR,mCAAmC,SAAS,MAAM,MAAM,IAAI,EAAE;MAElE;AAGA,UAAI,SAAS,WAAW,KAAK;AAG3B,YAAI,0BAA0B,OAAO,GAAG;AAEtC,eAAK,gBAAgB,EAAE,iBAAiB,OAAS,CAAE,EAAE,MAAM,SAAM;AAAA,gBAAAA;AAAC,oBAAAA,MAAA,KAAK,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,MAAG,GAAG;UAAC,CAAA;QACvF;AACA;MACF;AAGA,YAAM,WAAW,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAE5D,YAAM,cAAc,SAAS,OAAO,SAAO,YAAY,OAAO,QAAQ,OAAO,IAAI,OAAO,MAAS,EAAE,SAAS;AAG5G,YAAM,cAAc,SAAS,QAAQ,IAAI,cAAc;AAEvD,UAAI,aAAa;AACf,YAAI,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,SAAS,mBAAmB,GAAG;AAI9C,eAAK,iBAAiB,SAAS,MAAM,EAAE,kBAAiB,CAAE;QAC5D,WAAW,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,SAAS,kBAAkB,GAAG;AAEpD,gBAAM,OAAO,MAAM,SAAS,KAAI;AAChC,gBAAM,mBAAmB,MAAM,QAAQ,IAAI,IACvC,KAAK,IAAI,SAAO,qBAAqB,MAAM,GAAG,CAAC,IAC/C,CAAC,qBAAqB,MAAM,IAAI,CAAC;AAErC,qBAAW,OAAO,kBAAkB;AAClC,aAAA,KAAA,KAAK,eAAS,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,GAAG;UACtB;QACF,OAAO;AACL,gBAAM,IAAI,oBACR,IACA,4BAA4B,WAAW,EAAE;QAE7C;MACF;IACF,SAAS,OAAO;AACd,OAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,KAAc;AAC7B,YAAM;IACR;EACF;EAEA,IAAI,YAAS;AACX,WAAO,KAAK;EACd;;;;;;;;;;;;EAaA,MAAM,mBAAgB;;AACpB,QAAI,CAAC,KAAK,YAAY;AACpB;IACF;AAEA,QAAI;AACF,YAAM,UAAU,MAAM,KAAK,eAAc;AAEzC,YAAM,OAAO;QACX,GAAG,KAAK;QACR,QAAQ;QACR;QACA,SAAQ,KAAA,KAAK,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE;;AAGjC,YAAM,WAAW,MAAM,MAAM,KAAK,MAAM,IAAI;AAI5C,UAAI,CAAC,SAAS,MAAM,SAAS,WAAW,KAAK;AAC3C,cAAM,IAAI,oBACR,SAAS,QACT,gCAAgC,SAAS,UAAU,EAAE;MAEzD;AAEA,WAAK,aAAa;IACpB,SAAS,OAAO;AACd,OAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,KAAc;AAC7B,YAAM;IACR;EACF;;", "names": ["_a", "error"]}