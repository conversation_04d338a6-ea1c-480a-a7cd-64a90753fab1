# 🔧 Vapi 400 Bad Request Error - Complete Solution Guide

## 🎯 **Quick Fix Steps**

### **Step 1: Run the Debug Script**
Open your browser console and run this script to diagnose the issue:

```javascript
// Copy and paste this entire script into your browser console
const debugVapi400Error = async () => {
  console.log('🔍 Debugging Vapi 400 Bad Request Error');
  console.log('=====================================');

  // Check configuration
  const apiKey = '310f0d43-27c2-47a5-a76d-e55171d024f7';
  const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
  
  console.log('🔑 API Key:', `${apiKey.substring(0, 8)}...`);
  console.log('🤖 Assistant ID:', assistantId);

  // Test the API call
  try {
    const response = await fetch('https://api.vapi.ai/call/web', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        assistantId: assistantId
      })
    });

    console.log('📊 Response Status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Error Response:', errorText);
      
      if (response.status === 400) {
        console.log('\n🔧 SOLUTION: The assistant ID may not exist in your Vapi account');
        console.log('Go to https://dashboard.vapi.ai/assistants and check your assistants');
      }
    } else {
      const data = await response.json();
      console.log('✅ Success:', data);
    }
  } catch (error) {
    console.error('❌ Network Error:', error);
  }
};

debugVapi400Error();
```

### **Step 2: Check Your Vapi Dashboard**

1. **Go to your Vapi Dashboard**: https://dashboard.vapi.ai/assistants
2. **Verify the assistant exists**: Look for assistant ID `f9b97d13-f9c4-40af-a660-62ba5925ff2a`
3. **Check assistant status**: Ensure it's published and active

### **Step 3: Try Alternative Assistant ID**

If the assistant doesn't exist, try using a different assistant ID. Here's how to get one:

```javascript
// Create a new assistant or use an existing one
const createTestAssistant = async () => {
  const apiKey = '310f0d43-27c2-47a5-a76d-e55171d024f7';
  
  try {
    // First, list your existing assistants
    const listResponse = await fetch('https://api.vapi.ai/assistant', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });
    
    if (listResponse.ok) {
      const assistants = await listResponse.json();
      console.log('📋 Your existing assistants:', assistants);
      
      if (assistants.length > 0) {
        const firstAssistant = assistants[0];
        console.log('✅ Use this assistant ID:', firstAssistant.id);
        return firstAssistant.id;
      }
    }
    
    // If no assistants exist, create one
    const createResponse = await fetch('https://api.vapi.ai/assistant', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        name: 'LegalScout Assistant',
        model: {
          provider: 'openai',
          model: 'gpt-4o',
          messages: [{
            role: 'system',
            content: 'You are Scout, a helpful legal assistant for LegalScout. Help users with their legal questions in a friendly and professional manner.'
          }]
        },
        voice: {
          provider: 'playht',
          voiceId: 'sarah'
        }
      })
    });
    
    if (createResponse.ok) {
      const newAssistant = await createResponse.json();
      console.log('✅ Created new assistant:', newAssistant.id);
      return newAssistant.id;
    } else {
      const error = await createResponse.text();
      console.error('❌ Failed to create assistant:', error);
    }
  } catch (error) {
    console.error('❌ Error:', error);
  }
};

createTestAssistant();
```

### **Step 4: Update Your Code**

If you need to use a different assistant ID, update the constant:

```javascript
// In src/constants/vapiConstants.js
export const DEFAULT_ASSISTANT_ID = 'YOUR_NEW_ASSISTANT_ID_HERE';
```

### **Step 5: Test the Fix**

After updating the assistant ID, test the call:

```javascript
// Test the updated configuration
const testUpdatedCall = async () => {
  const apiKey = '310f0d43-27c2-47a5-a76d-e55171d024f7';
  const newAssistantId = 'YOUR_NEW_ASSISTANT_ID_HERE'; // Replace with actual ID
  
  try {
    const response = await fetch('https://api.vapi.ai/call/web', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        assistantId: newAssistantId
      })
    });
    
    if (response.ok) {
      console.log('✅ Call test successful!');
      const data = await response.json();
      console.log('📞 Call data:', data);
    } else {
      console.error('❌ Still getting error:', response.status);
    }
  } catch (error) {
    console.error('❌ Network error:', error);
  }
};

// testUpdatedCall(); // Uncomment after updating assistant ID
```

## 🔍 **Common Causes and Solutions**

### **1. Assistant Doesn't Exist (Most Common)**
- **Cause**: The assistant ID `f9b97d13-f9c4-40af-a660-62ba5925ff2a` doesn't exist in your Vapi account
- **Solution**: Use an existing assistant ID from your dashboard or create a new one

### **2. Assistant Not Published**
- **Cause**: The assistant exists but isn't published/active
- **Solution**: Go to your Vapi dashboard and publish the assistant

### **3. API Key Issues**
- **Cause**: Wrong API key or insufficient permissions
- **Solution**: Verify you're using the correct public API key

### **4. Request Format Issues**
- **Cause**: Incorrect request body or headers
- **Solution**: Use the exact format shown in the test scripts above

## 🚀 **Immediate Action Plan**

1. **Run the debug script** to identify the exact issue
2. **Check your Vapi dashboard** for existing assistants
3. **Update the assistant ID** if needed
4. **Test the fix** using the test script
5. **Restart your development server** after making changes

## 📞 **Emergency Fallback**

If you need an immediate working solution, you can temporarily use this test assistant creation approach:

```javascript
// Emergency assistant creation
window.createEmergencyAssistant = async () => {
  // This will create a basic working assistant for testing
  // Replace with your actual assistant configuration later
};
```

## 🎯 **Next Steps**

After fixing the 400 error:
1. Test the voice call functionality
2. Verify microphone permissions work
3. Check that transcripts are captured correctly
4. Ensure the call ends properly

The most likely cause is that the assistant ID doesn't exist in your Vapi account. Run the debug script first to confirm this.
