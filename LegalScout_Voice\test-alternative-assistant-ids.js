// Test Alternative Assistant IDs
// Run this in your browser console to find a working assistant ID

console.log('🔍 Testing Alternative Assistant IDs');
console.log('====================================');

const apiKey = '310f0d43-27c2-47a5-a76d-e55171d024f7';

// Alternative assistant IDs from your codebase
const alternativeAssistantIds = [
  // Primary working assistant ID - now the default
  'eb8533fa-902e-46be-8ce9-df20f5c550d7', // damon attorney (confirmed working)

  // Former default - now marked as orphaned
  'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865',
  '368e963b-761c-45bb-91e9-8f96b8483f4d',
  'efcadef8-7d6e-49b1-91fb-e74d4223e695',
  'e0705a0d-0511-4d91-b185-56feba033b76',
  'edd3008b-ac5e-4026-97fb-d556cc1def1e',
  'a53344e3-1edb-4917-9b2d-fbd28f4e5dbf',
  'd1dae707-d4b9-4728-9a29-210ccc3b4f5e',
  '5b264951-c02d-43a0-99e0-902578192706',
  'fd273605-12af-438b-9fa4-31cc0dfb4af4',
  '2ecce4a5-a2ca-4a9a-a75d-8821b8294589'
];

const testAssistantId = async (assistantId, index) => {
  try {
    console.log(`\n${index + 1}. Testing: ${assistantId}`);
    
    const response = await fetch('https://api.vapi.ai/call/web', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        assistantId: assistantId
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ SUCCESS! This assistant ID works`);
      console.log(`   📞 Call data:`, data);
      return { assistantId, success: true, data };
    } else {
      const errorText = await response.text();
      console.log(`   ❌ Failed (${response.status}):`, errorText.substring(0, 100));
      return { assistantId, success: false, error: errorText };
    }
  } catch (error) {
    console.log(`   ❌ Network error:`, error.message);
    return { assistantId, success: false, error: error.message };
  }
};

const testAllAssistants = async () => {
  console.log(`\n🚀 Testing ${alternativeAssistantIds.length} alternative assistant IDs...\n`);
  
  const results = [];
  
  for (let i = 0; i < alternativeAssistantIds.length; i++) {
    const result = await testAssistantId(alternativeAssistantIds[i], i);
    results.push(result);
    
    // Add a small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Summary
  console.log('\n📊 SUMMARY:');
  console.log('============');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Working Assistant IDs: ${successful.length}`);
  successful.forEach(result => {
    console.log(`   ${result.assistantId} ✅`);
  });
  
  console.log(`\n❌ Failed Assistant IDs: ${failed.length}`);
  failed.forEach(result => {
    console.log(`   ${result.assistantId} ❌`);
  });
  
  if (successful.length > 0) {
    const firstWorking = successful[0];
    console.log(`\n🎯 RECOMMENDED ACTION:`);
    console.log(`Use this working assistant ID: ${firstWorking.assistantId}`);
    console.log(`\nUpdate your constants file:`);
    console.log(`export const DEFAULT_ASSISTANT_ID = '${firstWorking.assistantId}';`);
    
    // Store the working ID globally for easy access
    window.WORKING_ASSISTANT_ID = firstWorking.assistantId;
    console.log(`\n💾 Stored working ID in window.WORKING_ASSISTANT_ID`);
  } else {
    console.log(`\n⚠️ No working assistant IDs found. You may need to create a new assistant.`);
  }
  
  return results;
};

// Auto-run the test
testAllAssistants();

// Also provide a manual test function
window.testSingleAssistant = async (assistantId) => {
  console.log(`\n🧪 Manual test for: ${assistantId}`);
  const result = await testAssistantId(assistantId, 0);
  return result;
};

console.log(`\n💡 TIP: You can also test individual IDs with:`);
console.log(`window.testSingleAssistant('your-assistant-id-here')`);
