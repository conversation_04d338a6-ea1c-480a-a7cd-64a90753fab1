// Quick fix script for Vapi 400 Bad Request error
// This script addresses the most common causes of the error

console.log('🔧 Applying Vapi 400 Error Fixes');
console.log('=================================');

// Fix 1: Ensure proper API key configuration
const fixApiKey = () => {
  console.log('\n1. Fixing API Key Configuration...');
  
  // Check if API key is available
  const envApiKey = import.meta?.env?.VITE_VAPI_PUBLIC_KEY;
  const windowApiKey = window.VITE_VAPI_PUBLIC_KEY;
  
  if (!envApiKey && !windowApiKey) {
    console.error('❌ No API key found. Please set VITE_VAPI_PUBLIC_KEY in your .env file');
    console.log('Add this to your .env file:');
    console.log('VITE_VAPI_PUBLIC_KEY=310f0d43-27c2-47a5-a76d-e55171d024f7');
    return false;
  }
  
  // Set window variable as fallback
  if (envApiKey && !windowApiKey) {
    window.VITE_VAPI_PUBLIC_KEY = envApiKey;
    console.log('✅ Set window.VITE_VAPI_PUBLIC_KEY from environment');
  }
  
  return true;
};

// Fix 2: Validate and fix assistant ID
const fixAssistantId = () => {
  console.log('\n2. Fixing Assistant ID...');
  
  // Use the production assistant ID
  const correctAssistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
  
  // Set it globally for the app to use
  window.DEFAULT_ASSISTANT_ID = correctAssistantId;
  
  console.log('✅ Set DEFAULT_ASSISTANT_ID:', correctAssistantId);
  return correctAssistantId;
};

// Fix 3: Create a working Vapi instance with proper error handling
const createFixedVapiInstance = async () => {
  console.log('\n3. Creating Fixed Vapi Instance...');
  
  const apiKey = import.meta?.env?.VITE_VAPI_PUBLIC_KEY || window.VITE_VAPI_PUBLIC_KEY;
  
  if (!apiKey) {
    console.error('❌ Cannot create Vapi instance without API key');
    return null;
  }
  
  try {
    // Ensure Vapi SDK is loaded
    if (typeof window.Vapi === 'undefined') {
      console.log('📦 Loading Vapi SDK...');
      
      // Load Vapi SDK dynamically
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.js';
      script.onload = () => {
        console.log('✅ Vapi SDK loaded');
        initializeVapi();
      };
      script.onerror = () => {
        console.error('❌ Failed to load Vapi SDK');
      };
      document.head.appendChild(script);
      return null;
    }
    
    return initializeVapi();
  } catch (error) {
    console.error('❌ Error creating Vapi instance:', error);
    return null;
  }
};

const initializeVapi = () => {
  const apiKey = import.meta?.env?.VITE_VAPI_PUBLIC_KEY || window.VITE_VAPI_PUBLIC_KEY;
  
  console.log('🚀 Initializing Vapi with key:', apiKey.substring(0, 8) + '...');
  
  const vapi = new window.Vapi(apiKey);
  
  // Set up basic event listeners
  vapi.on('call-start', () => {
    console.log('✅ Call started successfully');
  });
  
  vapi.on('call-end', () => {
    console.log('📞 Call ended');
  });
  
  vapi.on('error', (error) => {
    console.error('❌ Vapi error:', error);
  });
  
  // Store globally for debugging
  window.debugVapi = vapi;
  
  console.log('✅ Vapi instance created and stored in window.debugVapi');
  return vapi;
};

// Fix 4: Test the fixed configuration
const testFixedConfiguration = async () => {
  console.log('\n4. Testing Fixed Configuration...');
  
  const apiKey = import.meta?.env?.VITE_VAPI_PUBLIC_KEY || window.VITE_VAPI_PUBLIC_KEY;
  const assistantId = window.DEFAULT_ASSISTANT_ID;
  
  if (!apiKey || !assistantId) {
    console.error('❌ Missing required configuration');
    return false;
  }
  
  try {
    // Test direct API call
    const response = await fetch('https://api.vapi.ai/call/web', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        assistantId: assistantId
      })
    });
    
    if (response.ok) {
      console.log('✅ API test successful');
      return true;
    } else {
      const errorText = await response.text();
      console.error('❌ API test failed:', response.status, errorText);
      return false;
    }
  } catch (error) {
    console.error('❌ Network error during test:', error);
    return false;
  }
};

// Fix 5: Provide manual override function
const createManualStartCall = () => {
  console.log('\n5. Creating Manual Start Call Function...');
  
  window.manualStartCall = async () => {
    const apiKey = import.meta?.env?.VITE_VAPI_PUBLIC_KEY || window.VITE_VAPI_PUBLIC_KEY;
    const assistantId = window.DEFAULT_ASSISTANT_ID;
    
    if (!apiKey || !assistantId) {
      console.error('❌ Missing configuration for manual call');
      return;
    }
    
    try {
      console.log('📞 Starting manual call...');
      
      if (!window.debugVapi) {
        console.log('Creating new Vapi instance...');
        window.debugVapi = new window.Vapi(apiKey);
      }
      
      const call = await window.debugVapi.start(assistantId);
      console.log('✅ Manual call started:', call);
      return call;
    } catch (error) {
      console.error('❌ Manual call failed:', error);
      throw error;
    }
  };
  
  console.log('✅ Manual start call function available as window.manualStartCall()');
};

// Run all fixes
const runAllFixes = async () => {
  console.log('🔧 Running all fixes...\n');
  
  const apiKeyFixed = fixApiKey();
  const assistantId = fixAssistantId();
  
  if (apiKeyFixed) {
    await createFixedVapiInstance();
    const testPassed = await testFixedConfiguration();
    createManualStartCall();
    
    console.log('\n📋 Fix Summary:');
    console.log('================');
    console.log('✅ API Key:', apiKeyFixed ? 'Fixed' : 'Failed');
    console.log('✅ Assistant ID:', assistantId ? 'Fixed' : 'Failed');
    console.log('✅ API Test:', testPassed ? 'Passed' : 'Failed');
    
    if (testPassed) {
      console.log('\n🎉 All fixes applied successfully!');
      console.log('Try calling window.manualStartCall() to test');
    } else {
      console.log('\n⚠️ Some issues remain. Check the API test results above.');
    }
  }
};

// Auto-run fixes
runAllFixes();
