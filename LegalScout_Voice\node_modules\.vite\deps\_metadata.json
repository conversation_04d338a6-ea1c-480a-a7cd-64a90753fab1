{"hash": "4c2b9be1", "browserHash": "7943dc6a", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "e1528bf4", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "fabd5bd7", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "88063d43", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "4cb7b5f3", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "b10fd4cc", "needsInterop": true}, "@modelcontextprotocol/sdk/client/index.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/index.js", "file": "@modelcontextprotocol_sdk_client_index__js.js", "fileHash": "31cdb141", "needsInterop": false}, "@modelcontextprotocol/sdk/client/sse.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/sse.js", "file": "@modelcontextprotocol_sdk_client_sse__js.js", "fileHash": "3eb2dad1", "needsInterop": false}, "@modelcontextprotocol/sdk/client/streamableHttp.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/streamableHttp.js", "file": "@modelcontextprotocol_sdk_client_streamableHttp__js.js", "fileHash": "ca5d4f3c", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "5baba43c", "needsInterop": false}, "@vapi-ai/web": {"src": "../../@vapi-ai/web/dist/vapi.js", "file": "@vapi-ai_web.js", "fileHash": "d81fcf7e", "needsInterop": true}, "jose": {"src": "../../jose/dist/webapi/index.js", "file": "jose.js", "fileHash": "458c3a44", "needsInterop": false}, "leaflet": {"src": "../../leaflet/dist/leaflet-src.js", "file": "leaflet.js", "fileHash": "f21d8307", "needsInterop": true}, "lodash": {"src": "../../lodash/lodash.js", "file": "lodash.js", "fileHash": "c99ff805", "needsInterop": true}, "papaparse": {"src": "../../papaparse/papaparse.min.js", "file": "papaparse.js", "fileHash": "cc181e43", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "c6b4b720", "needsInterop": true}, "react-dom/server": {"src": "../../react-dom/server.browser.js", "file": "react-dom_server.js", "fileHash": "9c6cb01e", "needsInterop": true}, "react-icons/fa": {"src": "../../react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "2b957f9f", "needsInterop": false}, "react-icons/md": {"src": "../../react-icons/md/index.mjs", "file": "react-icons_md.js", "fileHash": "1ba0489c", "needsInterop": false}, "react-markdown": {"src": "../../react-markdown/index.js", "file": "react-markdown.js", "fileHash": "095beae7", "needsInterop": false}, "react-toastify": {"src": "../../react-toastify/dist/index.mjs", "file": "react-toastify.js", "fileHash": "f4b038da", "needsInterop": false}, "rehype-raw": {"src": "../../rehype-raw/index.js", "file": "rehype-raw.js", "fileHash": "002842c3", "needsInterop": false}, "rehype-sanitize": {"src": "../../rehype-sanitize/index.js", "file": "rehype-sanitize.js", "fileHash": "5a38c022", "needsInterop": false}, "remark-gfm": {"src": "../../remark-gfm/index.js", "file": "remark-gfm.js", "fileHash": "9efd92af", "needsInterop": false}, "uuid": {"src": "../../uuid/dist/esm-browser/index.js", "file": "uuid.js", "fileHash": "403625bd", "needsInterop": false}, "xlsx": {"src": "../../xlsx/xlsx.mjs", "file": "xlsx.js", "fileHash": "68bfe620", "needsInterop": false}}, "chunks": {"browser-2H53BKCB": {"file": "browser-2H53BKCB.js"}, "browser-L3WHH2Q2": {"file": "browser-L3WHH2Q2.js"}, "chunk-WLB7R6ZN": {"file": "chunk-WLB7R6ZN.js"}, "chunk-CUTKQHYX": {"file": "chunk-CUTKQHYX.js"}, "chunk-EFHT7PV7": {"file": "chunk-EFHT7PV7.js"}, "chunk-A5ED6EHL": {"file": "chunk-A5ED6EHL.js"}, "chunk-ZKFSI6YS": {"file": "chunk-ZKFSI6YS.js"}, "chunk-3TH3G7JX": {"file": "chunk-3TH3G7JX.js"}, "chunk-Q72EVS5P": {"file": "chunk-Q72EVS5P.js"}, "chunk-73YGM6QR": {"file": "chunk-73YGM6QR.js"}, "chunk-2N3A5BUM": {"file": "chunk-2N3A5BUM.js"}, "chunk-FYR2ONTC": {"file": "chunk-FYR2ONTC.js"}, "chunk-J55MB4ZD": {"file": "chunk-J55MB4ZD.js"}, "chunk-SJSTY3YX": {"file": "chunk-SJSTY3YX.js"}, "chunk-C3M7BXFS": {"file": "chunk-C3M7BXFS.js"}}}