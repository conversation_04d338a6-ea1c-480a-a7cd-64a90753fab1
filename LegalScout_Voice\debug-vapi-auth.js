/**
 * Debug script to diagnose Vapi authorization issues
 * Copy and paste this entire script into the browser console to run it
 */

(async function debugVapiAuth() {
  console.log('🔍 Starting Vapi Authorization Debug...');

  // Check environment variables
  console.log('📋 Environment Variables Check:');

  // Check if we're in browser environment
  if (typeof window === 'undefined') {
    console.error('❌ This script must be run in a browser environment');
    return;
  }

  // Check import.meta.env (Vite environment)
  let envKey = null;
  try {
    if (typeof import !== 'undefined' && import.meta && import.meta.env) {
      envKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
      console.log('- import.meta.env.VITE_VAPI_PUBLIC_KEY:', envKey ? 'SET' : 'NOT SET');
    } else {
      console.log('- import.meta.env: NOT AVAILABLE');
    }
  } catch (e) {
    console.log('- import.meta.env: ERROR -', e.message);
  }

  console.log('- window.VITE_VAPI_PUBLIC_KEY:', window.VITE_VAPI_PUBLIC_KEY ? 'SET' : 'NOT SET');

  // Check actual values (first 8 chars only for security)
  const windowKey = window.VITE_VAPI_PUBLIC_KEY;

  if (envKey) {
    console.log('- Environment key preview:', envKey.substring(0, 8) + '...');
  }
  if (windowKey) {
    console.log('- Window key preview:', windowKey.substring(0, 8) + '...');
  }

  // Check Vapi configuration
  console.log('\n🔧 Vapi Configuration Check:');
  try {
    const { getVapiApiKey, validateVapiConfig } = await import('./src/config/vapiConfig.js');

    const clientKey = getVapiApiKey('client');
    const serverKey = getVapiApiKey('server');

    console.log('- Client key:', clientKey ? clientKey.substring(0, 8) + '...' : 'NOT SET');
    console.log('- Server key:', serverKey ? serverKey.substring(0, 8) + '...' : 'NOT SET');

    const validation = validateVapiConfig();
    console.log('- Configuration valid:', validation.isValid);
    console.log('- Warnings:', validation.warnings);
  } catch (error) {
    console.error('- Error loading Vapi config:', error);
  }

  // Check Vapi SDK availability
  console.log('\n📦 Vapi SDK Check:');
  console.log('- window.Vapi available:', typeof window.Vapi);
  console.log('- @vapi-ai/web module:', typeof window['@vapi-ai/web']);

  // Try to load Vapi SDK
  console.log('\n🚀 Testing Vapi SDK Loading:');
  try {
    let VapiClass = null;

    // Try window.Vapi first
    if (window.Vapi && typeof window.Vapi === 'function') {
      VapiClass = window.Vapi;
      console.log('✅ Using window.Vapi');
    } else {
      // Try dynamic import
      console.log('🔄 Attempting dynamic import...');
      const VapiModule = await import('@vapi-ai/web');
      VapiClass = VapiModule.default || VapiModule.Vapi || VapiModule;
      console.log('✅ Loaded via dynamic import');
    }

    if (VapiClass) {
      console.log('- Vapi class type:', typeof VapiClass);
      console.log('- Vapi constructor:', VapiClass.toString().substring(0, 100) + '...');

      // Try to create instance with API key
      const apiKey = envKey || windowKey || '310f0d43-27c2-47a5-a76d-e55171d024f7';
      console.log('\n🔑 Testing Vapi Instance Creation:');
      console.log('- Using API key:', apiKey.substring(0, 8) + '...');

      try {
        const vapiInstance = new VapiClass(apiKey);
        console.log('✅ Vapi instance created successfully');
        console.log('- Instance type:', typeof vapiInstance);
        console.log('- Instance methods:', Object.getOwnPropertyNames(vapiInstance).slice(0, 10));

        // Store for debugging
        window.debugVapiInstance = vapiInstance;
        console.log('- Instance stored in window.debugVapiInstance');

      } catch (instanceError) {
        console.error('❌ Failed to create Vapi instance:', instanceError);
        console.error('- Error message:', instanceError.message);
        console.error('- Error stack:', instanceError.stack);
      }
    } else {
      console.error('❌ Could not load Vapi class');
    }

  } catch (loadError) {
    console.error('❌ Failed to load Vapi SDK:', loadError);
  }

  // Check network connectivity to Vapi API
  console.log('\n🌐 Testing Vapi API Connectivity:');
  const testApiKey = envKey || windowKey || '310f0d43-27c2-47a5-a76d-e55171d024f7';

  fetch('https://api.vapi.ai/assistant', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${testApiKey}`,
      'Content-Type': 'application/json'
    }
  })
  .then(response => {
    console.log('- API Response Status:', response.status);
    console.log('- API Response OK:', response.ok);

    if (response.status === 401) {
      console.error('❌ API Key is invalid or unauthorized');
    } else if (response.status === 200) {
      console.log('✅ API Key is valid');
    } else {
      console.warn('⚠️ Unexpected response status');
    }

    return response.text();
  })
  .then(text => {
    console.log('- API Response Body:', text.substring(0, 200) + '...');
  })
  .catch(error => {
    console.error('❌ API Request failed:', error);
  });

  console.log('\n🏁 Debug complete. Check the logs above for issues.');
})();
