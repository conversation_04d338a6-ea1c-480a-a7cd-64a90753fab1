/**
 * Quick fix for Vapi authorization issues
 * This script ensures the API key is properly loaded and passed to Vapi instances
 */

// Ensure environment variables are loaded
console.log('🔧 Fixing Vapi Authorization Issues...');

// Step 1: Verify API key is available
function getVapiApiKey() {
  // Try multiple sources for the API key
  let apiKey = null;
  
  // Try import.meta.env (Vite)
  if (typeof import !== 'undefined' && import.meta && import.meta.env) {
    apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
  }
  
  // Try window object
  if (!apiKey && typeof window !== 'undefined') {
    apiKey = window.VITE_VAPI_PUBLIC_KEY;
  }
  
  // Fallback to hardcoded key (from your .env file)
  if (!apiKey) {
    apiKey = '310f0d43-27c2-47a5-a76d-e55171d024f7';
  }
  
  console.log('🔑 Using API key:', apiKey ? apiKey.substring(0, 8) + '...' : 'NOT FOUND');
  return apiKey;
}

// Step 2: Patch Vapi configuration to ensure API key is always passed
function patchVapiConfig() {
  const apiKey = getVapiApiKey();
  
  if (!apiKey) {
    console.error('❌ No Vapi API key found!');
    return false;
  }
  
  // Store API key in window for global access
  window.VAPI_API_KEY = apiKey;
  window.VITE_VAPI_PUBLIC_KEY = apiKey;
  
  console.log('✅ API key stored in window object');
  return true;
}

// Step 3: Patch Vapi instance creation to always include API key
function patchVapiInstanceCreation() {
  // Wait for Vapi to be available
  const checkVapi = () => {
    if (window.Vapi && typeof window.Vapi === 'function') {
      console.log('📦 Vapi SDK detected, patching...');
      
      // Store original constructor
      const OriginalVapi = window.Vapi;
      
      // Create patched constructor
      window.Vapi = function(apiKey) {
        // Ensure API key is always provided
        const finalApiKey = apiKey || window.VAPI_API_KEY || getVapiApiKey();
        
        if (!finalApiKey) {
          console.error('❌ No API key provided to Vapi constructor');
          throw new Error('Vapi API key is required');
        }
        
        console.log('🔑 Creating Vapi instance with key:', finalApiKey.substring(0, 8) + '...');
        
        // Call original constructor with guaranteed API key
        return new OriginalVapi(finalApiKey);
      };
      
      // Copy static methods and properties
      Object.setPrototypeOf(window.Vapi, OriginalVapi);
      Object.assign(window.Vapi, OriginalVapi);
      
      console.log('✅ Vapi constructor patched');
      return true;
    }
    return false;
  };
  
  // Try immediately
  if (checkVapi()) {
    return;
  }
  
  // If not available, wait for it
  console.log('⏳ Waiting for Vapi SDK to load...');
  const interval = setInterval(() => {
    if (checkVapi()) {
      clearInterval(interval);
    }
  }, 100);
  
  // Timeout after 10 seconds
  setTimeout(() => {
    clearInterval(interval);
    console.warn('⚠️ Vapi SDK not detected after 10 seconds');
  }, 10000);
}

// Step 4: Apply all fixes
function applyVapiFixes() {
  console.log('🚀 Applying Vapi authorization fixes...');
  
  // Patch configuration
  if (!patchVapiConfig()) {
    return false;
  }
  
  // Patch instance creation
  patchVapiInstanceCreation();
  
  console.log('✅ Vapi fixes applied successfully');
  return true;
}

// Auto-apply fixes when script loads
if (typeof window !== 'undefined') {
  // Apply fixes immediately
  applyVapiFixes();
  
  // Also apply when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyVapiFixes);
  }
  
  // Store functions globally for manual use
  window.applyVapiFixes = applyVapiFixes;
  window.getVapiApiKey = getVapiApiKey;
  
  console.log('🔧 Vapi fix functions available globally:');
  console.log('- window.applyVapiFixes()');
  console.log('- window.getVapiApiKey()');
} else {
  console.log('ℹ️ This script is designed for browser environment');
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    applyVapiFixes,
    getVapiApiKey,
    patchVapiConfig,
    patchVapiInstanceCreation
  };
}
