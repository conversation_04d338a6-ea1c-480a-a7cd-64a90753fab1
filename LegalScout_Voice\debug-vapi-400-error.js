// Debug script for Vapi 400 Bad Request error
// Run this in your browser console to diagnose the issue

console.log('🔍 Debugging Vapi 400 Bad Request Error');
console.log('=====================================');

// 1. Check API Key Configuration
console.log('\n1. API Key Configuration:');
const envApiKey = '310f0d43-27c2-47a5-a76d-e55171d024f7'; // From your .env file
const windowApiKey = window.VITE_VAPI_PUBLIC_KEY;
console.log('- VITE_VAPI_PUBLIC_KEY (env):', envApiKey ? `${envApiKey.substring(0, 8)}...` : 'NOT SET');
console.log('- window.VITE_VAPI_PUBLIC_KEY:', windowApiKey ? `${windowApiKey.substring(0, 8)}...` : 'NOT SET');

// 2. Check Assistant ID
console.log('\n2. Assistant ID Configuration:');
const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'; // From your constants
console.log('- Assistant ID:', assistantId);
console.log('- Assistant ID format valid:', /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(assistantId));

// 3. Test Vapi API directly
console.log('\n3. Testing Vapi API directly:');
const testVapiAPI = async () => {
  const apiKey = envApiKey || windowApiKey;

  if (!apiKey) {
    console.error('❌ No API key found - cannot test API');
    return;
  }

  try {
    console.log('📡 Testing direct API call to Vapi...');
    console.log('🔑 Using API key:', `${apiKey.substring(0, 8)}...`);
    console.log('🤖 Using assistant ID:', assistantId);

    const response = await fetch('https://api.vapi.ai/call/web', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        assistantId: assistantId
      })
    });

    console.log('📊 Response status:', response.status);
    console.log('📊 Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error Response:', errorText);

      // Try to parse as JSON for better error details
      try {
        const errorJson = JSON.parse(errorText);
        console.error('📋 Parsed error details:', errorJson);
      } catch (e) {
        console.log('📋 Raw error text:', errorText);
      }

      // Parse common error scenarios
      if (response.status === 400) {
        console.log('\n🔧 Common 400 Error Causes:');
        console.log('- Invalid assistant ID format');
        console.log('- Assistant ID does not exist in your Vapi account');
        console.log('- Missing required parameters');
        console.log('- Invalid API key format');
        console.log('- Assistant is not published/active');
      } else if (response.status === 401) {
        console.log('\n🔧 401 Error: Invalid API key');
      } else if (response.status === 403) {
        console.log('\n🔧 403 Error: API key lacks permissions');
      }
    } else {
      const data = await response.json();
      console.log('✅ API call successful:', data);
    }
  } catch (error) {
    console.error('❌ Network error:', error);
  }
};

// 4. Check browser environment
console.log('\n4. Browser Environment:');
console.log('- User Agent:', navigator.userAgent);
console.log('- HTTPS:', location.protocol === 'https:');
console.log('- Microphone available:', !!navigator.mediaDevices?.getUserMedia);

// 5. Check for common issues
console.log('\n5. Common Issue Checks:');
console.log('- API key starts with correct prefix:', (envApiKey || windowApiKey || '').startsWith('310f0d43') ? '✅' : '❌');
console.log('- Assistant ID is UUID format:', /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(assistantId) ? '✅' : '❌');

// Run the API test
testVapiAPI();

// 6. Provide troubleshooting steps
console.log('\n6. Troubleshooting Steps:');
console.log('=====================================');
console.log('If you see a 400 error, try these steps:');
console.log('');
console.log('1. Verify API Key:');
console.log('   - Check your .env file has VITE_VAPI_PUBLIC_KEY set');
console.log('   - Ensure the key starts with your account prefix');
console.log('   - Restart your dev server after changing .env');
console.log('');
console.log('2. Verify Assistant ID:');
console.log('   - Log into Vapi dashboard and check your assistant ID');
console.log('   - Ensure the assistant exists and is active');
console.log('   - Copy the exact ID from the dashboard');
console.log('');
console.log('3. Check Network:');
console.log('   - Ensure you\'re on HTTPS (required for microphone)');
console.log('   - Check browser console for CORS errors');
console.log('   - Try in an incognito window');
console.log('');
console.log('4. Test with curl:');
console.log(`   curl -X POST https://api.vapi.ai/call/web \\`);
console.log(`     -H "Content-Type: application/json" \\`);
console.log(`     -H "Authorization: Bearer YOUR_API_KEY" \\`);
console.log(`     -d '{"assistantId": "${assistantId}"}'`);
