/**
 * Attorney Utilities
 *
 * This module provides utility functions for managing the relationship between
 * Auth0 users and attorney profiles in Supabase.
 */

import { supabase } from '../lib/supabase';

/**
 * Finds an attorney profile by user ID
 * @param {string} userId - The Auth0 user ID
 * @returns {Promise<Object|null>} - The attorney profile or null if not found
 */
export const findAttorneyByUserId = async (userId) => {
  if (!userId) return null;

  try {
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error finding attorney by user ID:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception finding attorney by user ID:', error);
    return null;
  }
};

/**
 * Finds an attorney profile by email
 * @param {string} email - The attorney's email address
 * @returns {Promise<Object|null>} - The attorney profile or null if not found
 */
export const findAttorneyByEmail = async (email) => {
  if (!email) return null;

  try {
    // Handle duplicates by getting the most recent attorney
    const { data: attorneys, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', email)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Error finding attorney by email:', error);
      return null;
    }

    if (attorneys && attorneys.length > 0) {
      if (attorneys.length > 1) {
        console.warn(`Found ${attorneys.length} attorneys for email ${email}, using the most recent one`);
      }
      return attorneys[0];
    }

    return null;
  } catch (error) {
    console.error('Exception finding attorney by email:', error);
    return null;
  }
};

/**
 * Finds an attorney profile by subdomain
 * @param {string} subdomain - The attorney's subdomain
 * @returns {Promise<Object|null>} - The attorney profile or null if not found
 */
export const findAttorneyBySubdomain = async (subdomain) => {
  if (!subdomain) return null;

  try {
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('subdomain', subdomain)
      .single();

    if (error) {
      console.error('Error finding attorney by subdomain:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception finding attorney by subdomain:', error);
    return null;
  }
};

/**
 * Finds an attorney profile by ID
 * @param {string} id - The attorney's ID
 * @returns {Promise<Object|null>} - The attorney profile or null if not found
 */
export const findAttorneyById = async (id) => {
  if (!id || id === 'undefined') return null;

  try {
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error finding attorney by ID:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception finding attorney by ID:', error);
    return null;
  }
};

/**
 * Finds an attorney profile using multiple identifiers in order of reliability
 * @param {Object} identifiers - Object containing possible identifiers
 * @param {string} identifiers.id - Attorney ID
 * @param {string} identifiers.userId - Auth0 user ID
 * @param {string} identifiers.email - Attorney email
 * @param {string} identifiers.subdomain - Attorney subdomain
 * @returns {Promise<Object|null>} - The attorney profile or null if not found
 */
export const findAttorney = async ({ id, userId, email, subdomain }) => {
  // Try each identifier in order of reliability
  let attorney = null;

  // First try to get the attorney ID from localStorage
  try {
    // Try multiple storage locations for the attorney ID
    let storedAttorneyId = localStorage.getItem('currentAttorneyId') || localStorage.getItem('attorney_id');
    if (storedAttorneyId === 'undefined') {
      console.warn('[attorneyUtils] Ignoring invalid stored attorney ID:', storedAttorneyId);
      storedAttorneyId = null;
    }

    if (storedAttorneyId) {
      console.log('[attorneyUtils] Found attorney ID in localStorage:', storedAttorneyId);
      attorney = await findAttorneyById(storedAttorneyId);
      if (attorney) {
        console.log('[attorneyUtils] Found attorney by ID from localStorage:', attorney);

        // Store the ID in all storage locations for redundancy
        try {
          localStorage.setItem('currentAttorneyId', attorney.id);
          localStorage.setItem('attorney_id', attorney.id);
        } catch (e) {
          console.error('[attorneyUtils] Error storing attorney ID in localStorage:', e);
        }

        return attorney;
      }
    }

    // Try to get the attorney from the 'attorney' object in localStorage
    const storedAttorney = localStorage.getItem('attorney');
    if (storedAttorney) {
      try {
        const parsedAttorney = JSON.parse(storedAttorney);
        if (parsedAttorney && parsedAttorney.id) {
          console.log('[attorneyUtils] Found attorney ID in stored attorney object:', parsedAttorney.id);
          attorney = await findAttorneyById(parsedAttorney.id);
          if (attorney) {
            console.log('[attorneyUtils] Found attorney by ID from stored attorney object:', attorney);

            // Store the ID in all storage locations for redundancy
            try {
              localStorage.setItem('currentAttorneyId', attorney.id);
              localStorage.setItem('attorney_id', attorney.id);
            } catch (e) {
              console.error('[attorneyUtils] Error storing attorney ID in localStorage:', e);
            }

            return attorney;
          }
        }
      } catch (e) {
        console.error('[attorneyUtils] Error parsing stored attorney:', e);
      }
    }
  } catch (e) {
    console.error('[attorneyUtils] Error getting attorney ID from localStorage:', e);
  }

  if (id) {
    attorney = await findAttorneyById(id);
    if (attorney) {
      console.log('[attorneyUtils] Found attorney by ID:', attorney);
      return attorney;
    }
  }

  if (userId) {
    attorney = await findAttorneyByUserId(userId);
    if (attorney) {
      console.log('[attorneyUtils] Found attorney by user ID:', attorney);
      // Store the attorney ID in all storage locations for redundancy
      try {
        localStorage.setItem('currentAttorneyId', attorney.id);
        localStorage.setItem('attorney_id', attorney.id);

        // Also store the entire attorney object for redundancy
        localStorage.setItem('attorney', JSON.stringify(attorney));
      } catch (e) {
        console.error('[attorneyUtils] Error storing attorney ID in localStorage:', e);
      }
      return attorney;
    }
  }

  if (email) {
    attorney = await findAttorneyByEmail(email);
    if (attorney) {
      console.log('[attorneyUtils] Found attorney by email:', attorney);
      // Store the attorney ID in all storage locations for redundancy
      try {
        localStorage.setItem('currentAttorneyId', attorney.id);
        localStorage.setItem('attorney_id', attorney.id);

        // Also store the entire attorney object for redundancy
        localStorage.setItem('attorney', JSON.stringify(attorney));
      } catch (e) {
        console.error('[attorneyUtils] Error storing attorney ID in localStorage:', e);
      }
      return attorney;
    }
  }

  if (subdomain) {
    attorney = await findAttorneyBySubdomain(subdomain);
    if (attorney) {
      console.log('[attorneyUtils] Found attorney by subdomain:', attorney);
      // Store the attorney ID in all storage locations for redundancy
      try {
        localStorage.setItem('currentAttorneyId', attorney.id);
        localStorage.setItem('attorney_id', attorney.id);

        // Also store the entire attorney object for redundancy
        localStorage.setItem('attorney', JSON.stringify(attorney));
      } catch (e) {
        console.error('[attorneyUtils] Error storing attorney ID in localStorage:', e);
      }
      return attorney;
    }
  }

  return null;
};

/**
 * Creates a new attorney profile
 * @param {Object} attorneyData - The attorney data
 * @returns {Promise<Object|null>} - The created attorney profile or null if failed
 */
export const createAttorney = async (attorneyData) => {
  if (!attorneyData) return null;

  try {
    // Generate a unique subdomain if not provided
    if (!attorneyData.subdomain) {
      let baseSubdomain = '';

      // Prefer firm name for subdomain generation
      if (attorneyData.firm_name) {
        baseSubdomain = attorneyData.firm_name
          .toLowerCase()
          .replace(/[^a-z0-9\s]/g, '') // Remove special characters except spaces
          .replace(/\s+/g, '-') // Replace spaces with hyphens
          .replace(/-+/g, '-') // Replace multiple hyphens with single
          .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
          .substring(0, 30); // Limit length
      } else if (attorneyData.email) {
        // Fallback to email prefix if no firm name
        baseSubdomain = attorneyData.email.split('@')[0].toLowerCase().replace(/[^a-z0-9]/g, '');
      } else {
        // Last resort fallback
        baseSubdomain = 'attorney';
      }

      // Check if the subdomain already exists
      const { data: existingAttorney } = await supabase
        .from('attorneys')
        .select('subdomain')
        .eq('subdomain', baseSubdomain)
        .single();

      if (existingAttorney) {
        // If subdomain exists, try variations before adding numbers
        let finalSubdomain = baseSubdomain;
        let suffix = 1;

        // Try adding common legal suffixes first
        const legalSuffixes = ['law', 'legal', 'firm', 'group'];
        for (const legalSuffix of legalSuffixes) {
          const candidate = `${baseSubdomain}-${legalSuffix}`;
          const { data: existingCandidate } = await supabase
            .from('attorneys')
            .select('subdomain')
            .eq('subdomain', candidate)
            .single();

          if (!existingCandidate) {
            finalSubdomain = candidate;
            break;
          }
        }

        // If legal suffixes don't work, add numbers
        if (finalSubdomain === baseSubdomain) {
          do {
            finalSubdomain = `${baseSubdomain}-${suffix}`;
            suffix++;

            const { data: existingNumbered } = await supabase
              .from('attorneys')
              .select('subdomain')
              .eq('subdomain', finalSubdomain)
              .single();

            if (!existingNumbered) break;
          } while (suffix < 100); // Prevent infinite loop
        }

        attorneyData.subdomain = finalSubdomain;
      } else {
        attorneyData.subdomain = baseSubdomain;
      }
    }

    // Create the attorney record
    const { data, error } = await supabase
      .from('attorneys')
      .insert([attorneyData])
      .select()
      .single();

    if (error) {
      console.error('Error creating attorney:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception creating attorney:', error);
    return null;
  }
};

/**
 * Updates an attorney profile
 * @param {string} id - The attorney ID
 * @param {Object} updateData - The data to update
 * @returns {Promise<Object|null>} - The updated attorney profile or null if failed
 */
export const updateAttorney = async (id, updateData) => {
  if (!id || !updateData) return null;

  try {
    // Filter update data to only include fields that exist in the database
    const filteredUpdateData = await filterUpdateData('attorneys', updateData);

    // Update the attorney record
    const { data, error } = await supabase
      .from('attorneys')
      .update(filteredUpdateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating attorney:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception updating attorney:', error);
    return null;
  }
};

/**
 * Filter update data to only include fields that exist in the database
 * @param {string} tableName - The name of the table
 * @param {Object} data - The data to filter
 * @returns {Promise<Object>} - The filtered data
 */
export const filterUpdateData = async (tableName, data) => {
  try {
    // Get the table schema
    const { data: columns, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);

    if (error) {
      console.error(`[filterUpdateData] Error getting schema for ${tableName}:`, error);
      return data; // Return original data if we can't get the schema
    }

    // If we got a row, use its keys as the column names
    if (columns && columns.length > 0) {
      const validColumns = Object.keys(columns[0]);
      const filteredData = {};

      // Only include fields that exist in the database
      Object.keys(data).forEach(key => {
        if (validColumns.includes(key)) {
          filteredData[key] = data[key];
        } else {
          console.warn(`[filterUpdateData] Column '${key}' not found in '${tableName}' schema, excluding from update`);
        }
      });

      console.log(`[filterUpdateData] Filtered update data for ${tableName}:`, filteredData);
      return filteredData;
    }

    return data; // Return original data if we couldn't determine the schema
  } catch (error) {
    console.error('[filterUpdateData] Exception:', error);
    return data; // Return original data on error
  }
};

/**
 * Ensures an attorney has a Vapi assistant ID
 * @param {Object} attorney - The attorney profile
 * @returns {Promise<Object>} - The attorney profile with a Vapi assistant ID
 */
export const ensureVapiAssistant = async (attorney) => {
  if (!attorney) return null;

  // If the attorney already has a Vapi assistant ID, return the attorney
  if (attorney.vapi_assistant_id) {
    return attorney;
  }

  try {
    // Create a new Vapi assistant
    // This would normally call the Vapi API, but for now we'll use a default ID
    const vapiAssistantId = 'eb8533fa-902e-46be-8ce9-df20f5c550d7'; // Confirmed working assistant ID

    // Update the attorney record with the Vapi assistant ID
    const { data, error } = await supabase
      .from('attorneys')
      .update({ vapi_assistant_id: vapiAssistantId })
      .eq('id', attorney.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating attorney with Vapi assistant ID:', error);

      // Return the attorney with the Vapi assistant ID even if the update failed
      return {
        ...attorney,
        vapi_assistant_id: vapiAssistantId
      };
    }

    return data;
  } catch (error) {
    console.error('Exception ensuring Vapi assistant:', error);

    // Return the attorney with a default Vapi assistant ID
    return {
      ...attorney,
      vapi_assistant_id: 'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865' // Default assistant ID
    };
  }
};
