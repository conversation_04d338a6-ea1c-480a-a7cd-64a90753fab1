# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_KEY=your-anon-key-from-supabase-dashboard
VITE_SUPABASE_ANON_KEY=your-anon-key-from-supabase-dashboard

# Vapi Configuration
VITE_VAPI_PUBLIC_KEY=your-vapi-public-key

# Apify Configuration (optional)
VITE_APIFY_API_TOKEN=your-apify-api-token

# MCP Configuration (optional)
MCP_API_KEY=your-mcp-api-key

# Vapi MCP Server Configuration (same as your Vapi API key)
VAPI_TOKEN=your-vapi-api-key

# Gmail OAuth Configuration (optional)
VITE_GMAIL_CLIENT_ID=your-gmail-client-id
VITE_GMAIL_REDIRECT_URI=http://localhost:5173/auth/callback

# Bug Reporter Configuration
# Required - Slack webhook URL for sending bug reports
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# Optional - For screenshot uploads (requires Slack bot token)
SLACK_BOT_TOKEN=xoxb-your-bot-token
SLACK_CHANNEL_ID=C1234567890

# Feature Flags
VITE_FALLBACK_MODE=false