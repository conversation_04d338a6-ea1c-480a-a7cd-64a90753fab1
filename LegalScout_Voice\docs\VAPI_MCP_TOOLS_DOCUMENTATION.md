# Vapi MCP Tools Documentation

This document provides a comprehensive overview of the Vapi Model Context Protocol (MCP) integration and the tools available in the LegalScout platform.

## Overview

LegalScout leverages Vapi's voice AI capabilities through a set of specialized tools that enable real-time data processing, attorney matching, and call management. These tools are integrated with Make.com webhooks to enable automated workflows beyond the conversation itself.

## MCP Integration

The Model Context Protocol (MCP) allows LegalScout to dynamically access Vapi's tools during calls. This integration enables:

1. Programmatic control of Vapi voice agents
2. Dynamic call routing and management
3. Access to specialized tools for legal intake and attorney matching
4. Integration with external systems via webhooks

### Configuration

The Vapi MCP server is configured with the following parameters:

- **API Key**: `6734febc-fc65-4669-93b0-929b31ff6564`
- **Default Assistant ID**: `eb8533fa-902e-46be-8ce9-df20f5c550d7`
- **MCP Server URL**: `https://mcp.vapi.ai/sse`

## Available Tools

### 1. LIVE_DOSSIER (ID: 4a0d63cf-0b84-4eec-bddf-9c5869439d7e)

**Purpose:** Updates case information in real-time during the conversation

**Features:**
- Creates and updates a structured case brief
- Supports emoji-prefixed fields for visual clarity
- Integrates with Make.com via webhook
- Drives the visual display in the application (interactive map, etc.)

**Data Structure:**
```json
{
  "🚨Urgency": "High/Medium/Low",
  "Case Dossier": {
    "📁 Client Background": "Client information collected",
    "🔄 STATUS": "Current case status",
    "📍 Jurisdiction": {
      "address": "Location information",
      "lat": 40.712776,
      "lng": -74.005974
    },
    "📝 Statement of Facts": "Detailed case facts",
    "⚖️ Legal Issues": "Legal issues identified",
    "🎯 Objectives": "Client's desired outcomes"
  }
}
```

**Webhook URL:** `https://hook.us1.make.com/2c6ez4oq26ka5kv7h7zjhiy4v18l20t1`

**Usage Guidelines:**
- The tool should be called silently whenever the assistant's understanding of the user's situation changes
- Updates should be incremental, preserving previously collected information
- Location data should include both text address and coordinates when available
- Status field should reflect the current stage of the intake process

### 2. CHECK_MATCHING_ATTORNEYS (ID: 313b71f6-f4ea-44d4-a304-f5050e890693)

**Purpose:** Triggers attorney matching based on practice area and case details

**Features:**
- Matches user cases with appropriate attorneys
- Filters by practice area and jurisdiction
- Integrates with the LegalScout marketplace
- Returns attorney profiles that match the case criteria

**Webhook URL:** `https://hook.us1.make.com/4empo1t13htixhbkz7bs9ettvxs8g0j5`

**Usage Guidelines:**
- Call this tool when sufficient case information has been gathered
- Requires practice area and jurisdiction at minimum
- Results can be presented to the user as part of the conversation
- Can be used to refine the search based on user feedback

### 3. LOCAL_ATTORNEY_SEARCH (ID: 42375b0b-9c0b-481b-b445-d219b41f1988)

**Purpose:** Performs location-based attorney searches

**Features:**
- Searches for attorneys based on geographic location
- Can filter by distance/proximity
- Provides location-specific attorney recommendations
- Integrates with mapping functionality

**Webhook URL:** `https://hook.us1.make.com/g6k2w1raan6ovyodlvfoevdtuuw09hb6`

**Usage Guidelines:**
- Use when the user's location is known and they need local representation
- Can be combined with practice area filters
- Results include distance information when available
- Particularly useful for urgent matters requiring local counsel

### 4. GET_USER_INFO (ID: 40e60896-518c-470a-a713-7abc2cd0c924)

**Purpose:** Collects and processes user contact information

**Features:**
- Captures and validates user contact details
- Stores information for follow-up
- Integrates with CRM systems
- Ensures all necessary contact fields are collected

**Webhook URL:** `https://hook.us1.make.com/2c6ez4oq26ka5kv7h7zjhiy4v18l20t1`

**Usage Guidelines:**
- Call this tool when collecting contact information
- Ensure email addresses and phone numbers are confirmed by spelling back
- Required fields include name, email, phone number, and address
- Information is stored securely and used for attorney follow-up

### 5. Destination_Phone (ID: e470c69b-62af-475d-a727-652a40a46e35)

**Purpose:** Handles call transfers and connections to human representatives

**Features:**
- Transfers calls to appropriate human representatives
- Provides technical assistance connections
- Supports warm transfers with context
- Can be used for escalation when needed

**Usage Guidelines:**
- Use when a user explicitly requests to speak with a human
- Use for technical support issues that cannot be resolved by the assistant
- Use when complex legal situations require immediate human intervention
- Provide context about the conversation when transferring

## Integration with Business Logic

These tools support LegalScout's dual business model:

1. **LegalScout Agent (B2B SaaS Platform)**
   - Customizable AI agents for attorneys
   - Client intake and qualification
   - Call forwarding and routing
   - Integration with attorney calendars and CRMs

2. **LegalScout.net (B2C Platform & Lead Marketplace)**
   - Consumer-facing legal assistance
   - Lead generation and qualification
   - Attorney matching and referrals
   - Case brief creation and submission

## System Prompt Considerations

When designing system prompts for LegalScout assistants, consider:

1. **Tool Integration**: Include specific instructions for when and how to use each tool
2. **Data Formats**: Specify the expected data structure for each tool, especially LIVE_DOSSIER
3. **Conversation Flow**: Design a natural flow that gathers information while maintaining engagement
4. **Attorney Customization**: For attorney-specific assistants, include custom branding and qualification criteria
5. **Error Handling**: Provide fallbacks for when tools fail or return unexpected results

## Future Considerations

With the MCP integration in place, LegalScout can now:

1. Create custom assistants for individual attorneys programmatically
2. Implement advanced call routing based on availability and expertise
3. Integrate with additional external systems through new MCP tools
4. Enhance the lead marketplace with more sophisticated matching algorithms
5. Implement multi-agent scenarios with specialized roles

## References

- [Vapi MCP Server Documentation](https://docs.vapi.ai/sdk/mcp-server)
- [Model Context Protocol Documentation](https://modelcontextprotocol.io/)
- [Make.com Webhook Documentation](https://www.make.com/en/help/tools/webhooks)
- [LegalScout Project Memo](docs/LEGALSCOUT_PROJECT_MEMO.md)
